using System.Windows;

namespace MemoryEnhancer
{
    public partial class InputDialog : Window
    {
        public string InputText { get; private set; } = string.Empty;

        public InputDialog(string title, string prompt, string defaultText = "")
        {
            InitializeComponent();
            
            Title = title;
            promptText.Text = prompt;
            inputTextBox.Text = defaultText;
            
            // 选中默认文本
            inputTextBox.SelectAll();
            inputTextBox.Focus();
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            InputText = inputTextBox.Text?.Trim() ?? string.Empty;
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
