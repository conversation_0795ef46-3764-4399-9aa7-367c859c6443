<Window x:Class="MemoryEnhancer.InputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="输入对话框" Height="150" Width="350"
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize" Topmost="True">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock x:Name="promptText" Grid.Row="0" Text="请输入:" Margin="0,0,0,10"/>

        <TextBox x:Name="inputTextBox" Grid.Row="1" Height="25" Margin="0,0,0,10"/>

        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button x:Name="okButton" Content="确定" Width="75" Height="25" Margin="0,0,10,0"
                    Click="OkButton_Click" IsDefault="True"/>
            <Button x:Name="cancelButton" Content="取消" Width="75" Height="25"
                    Click="CancelButton_Click" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
