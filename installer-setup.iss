[Setup]
; 基本信息
AppId={{B8F9C8E5-4A2D-4F1E-9B3C-7D6E8F9A0B1C}
AppName=我要记忆
AppVersion=1.0.0
AppVerName=我要记忆 1.0.0
AppPublisher=周少文
AppPublisherURL=https://github.com/memory-enhancer
AppSupportURL=https://github.com/memory-enhancer/support
AppUpdatesURL=https://github.com/memory-enhancer/updates
DefaultDirName={autopf}\我要记忆
DefaultGroupName=我要记忆
AllowNoIcons=yes
LicenseFile=LICENSE.txt
InfoBeforeFile=安装和使用说明.md
OutputDir=installer-output
OutputBaseFilename=我要记忆_v1.0_Windows10+_安装版
SetupIconFile=Resources\icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

; 系统要求
MinVersion=10.0.17763
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; 权限要求
PrivilegesRequired=admin
PrivilegesRequiredOverridesAllowed=dialog

; 安装选项
DisableProgramGroupPage=yes
DisableReadyPage=no
DisableFinishedPage=no
DisableWelcomePage=no

[Languages]
Name: "chinesesimp"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode
Name: "startmenu"; Description: "添加到开始菜单"; GroupDescription: "{cm:AdditionalIcons}"; Flags: checkedonce
Name: "autostart"; Description: "开机自动启动"; GroupDescription: "启动选项"; Flags: unchecked

[Files]
; 主程序文件
Source: "publish-standalone\MemoryEnhancer.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "publish-standalone\*.dll"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "publish-standalone\*.json"; DestDir: "{app}"; Flags: ignoreversion
Source: "publish-standalone\*.pdb"; DestDir: "{app}"; Flags: ignoreversion

; 语言资源文件
Source: "publish-standalone\zh-Hans\*"; DestDir: "{app}\zh-Hans"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "publish-standalone\en\*"; DestDir: "{app}\en"; Flags: ignoreversion recursesubdirs createallsubdirs; Check: FileExists(ExpandConstant('{app}\en'))

; 文档文件
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "LICENSE.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "安装和使用说明.md"; DestDir: "{app}"; Flags: ignoreversion

; 资源文件
Source: "Resources\*"; DestDir: "{app}\Resources"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
; 开始菜单图标
Name: "{group}\我要记忆"; Filename: "{app}\MemoryEnhancer.exe"; IconFilename: "{app}\Resources\icon.ico"
Name: "{group}\卸载我要记忆"; Filename: "{uninstallexe}"
Name: "{group}\使用说明"; Filename: "{app}\安装和使用说明.md"

; 桌面图标
Name: "{autodesktop}\我要记忆"; Filename: "{app}\MemoryEnhancer.exe"; IconFilename: "{app}\Resources\icon.ico"; Tasks: desktopicon

; 快速启动图标
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\我要记忆"; Filename: "{app}\MemoryEnhancer.exe"; IconFilename: "{app}\Resources\icon.ico"; Tasks: quicklaunchicon

[Registry]
; 注册表项 - 程序信息
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{{B8F9C8E5-4A2D-4F1E-9B3C-7D6E8F9A0B1C}_is1"; ValueType: string; ValueName: "DisplayName"; ValueData: "我要记忆 1.0.0"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{{B8F9C8E5-4A2D-4F1E-9B3C-7D6E8F9A0B1C}_is1"; ValueType: string; ValueName: "DisplayVersion"; ValueData: "1.0.0"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\{{B8F9C8E5-4A2D-4F1E-9B3C-7D6E8F9A0B1C}_is1"; ValueType: string; ValueName: "Publisher"; ValueData: "周少文"

; 文件关联
Root: HKCR; Subkey: ".memory"; ValueType: string; ValueName: ""; ValueData: "MemoryEnhancerFile"
Root: HKCR; Subkey: "MemoryEnhancerFile"; ValueType: string; ValueName: ""; ValueData: "我要记忆文件"
Root: HKCR; Subkey: "MemoryEnhancerFile\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\Resources\icon.ico"
Root: HKCR; Subkey: "MemoryEnhancerFile\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\MemoryEnhancer.exe"" ""%1"""

; 开机自启动（可选）
Root: HKCU; Subkey: "Software\Microsoft\Windows\CurrentVersion\Run"; ValueType: string; ValueName: "我要记忆"; ValueData: """{app}\MemoryEnhancer.exe"""; Tasks: autostart

[Run]
; 安装完成后运行程序
Filename: "{app}\MemoryEnhancer.exe"; Description: "{cm:LaunchProgram,我要记忆}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
; 卸载时删除用户数据（可选）
Type: filesandordirs; Name: "{app}\Data"
Type: filesandordirs; Name: "{app}\Logs"
Type: files; Name: "{app}\*.log"

[Code]
// 检查系统要求
function InitializeSetup(): Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  
  // 检查Windows版本
  if (Version.Major < 10) or ((Version.Major = 10) and (Version.Build < 17763)) then
  begin
    MsgBox('此程序需要 Windows 10 版本 1809 (Build 17763) 或更高版本。' + #13#10 + 
           '您的系统版本过低，无法安装此程序。', mbError, MB_OK);
    Result := False;
  end
  else
    Result := True;
end;

// 检查是否已安装
function InitializeUninstall(): Boolean;
begin
  if MsgBox('确定要卸载我要记忆吗？' + #13#10 + #13#10 + 
            '这将删除程序文件，但保留您的学习数据。', 
            mbConfirmation, MB_YESNO) = IDYES then
    Result := True
  else
    Result := False;
end;

// 安装完成后的处理
procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    // 创建数据目录
    CreateDir(ExpandConstant('{app}\Data'));
    CreateDir(ExpandConstant('{app}\Logs'));
  end;
end;
