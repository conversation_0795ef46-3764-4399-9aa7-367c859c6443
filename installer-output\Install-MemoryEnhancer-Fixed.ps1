# Memory Enhancer - User-Friendly Installer
# This installer will stay open and show all messages

param(
    [string]$InstallPath = "$env:ProgramFiles\MemoryEnhancer"
)

# 设置窗口标题和编码
$Host.UI.RawUI.WindowTitle = "我要记忆 - 安装程序"
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 清屏并显示欢迎信息
Clear-Host

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    Write-Host $Text -ForegroundColor $Color
}

function Pause-WithMessage {
    param([string]$Message = "按任意键继续...")
    Write-Host ""
    Write-Host $Message -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# 显示欢迎界面
Write-ColorText "========================================" "Cyan"
Write-ColorText "我要记忆 v1.0 - 安装程序" "Cyan"
Write-ColorText "========================================" "Cyan"
Write-ColorText ""
Write-ColorText "版本: 1.0.0" "White"
Write-ColorText "发布者: 周少文" "White"
Write-ColorText "系统要求: Windows 10+ (x64)" "White"
Write-ColorText ""

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-ColorText "❌ 需要管理员权限" "Red"
    Write-ColorText ""
    Write-ColorText "此安装程序需要管理员权限才能安装到系统目录。" "Yellow"
    Write-ColorText "请右键点击此文件，选择'以管理员身份运行'。" "Yellow"
    Write-ColorText ""
    Pause-WithMessage "按任意键退出..."
    exit 1
}

Write-ColorText "✓ 管理员权限检查通过" "Green"

# 检查系统要求
$OSVersion = [System.Environment]::OSVersion.Version
if ($OSVersion.Major -lt 10 -or ($OSVersion.Major -eq 10 -and $OSVersion.Build -lt 17763)) {
    Write-ColorText "❌ 系统要求不满足" "Red"
    Write-ColorText "此程序需要 Windows 10 版本 1809 (Build 17763) 或更高版本" "Red"
    Write-ColorText "您的系统版本: $($OSVersion.Major).$($OSVersion.Minor) Build $($OSVersion.Build)" "Yellow"
    Pause-WithMessage "按任意键退出..."
    exit 1
}

if (-not [Environment]::Is64BitOperatingSystem) {
    Write-ColorText "❌ 系统架构不支持" "Red"
    Write-ColorText "此程序需要 64 位系统" "Red"
    Pause-WithMessage "按任意键退出..."
    exit 1
}

Write-ColorText "✓ 系统要求检查通过" "Green"
Write-ColorText ""

# 检查是否已安装
if (Test-Path "$InstallPath\MemoryEnhancer.exe") {
    Write-ColorText "⚠️ 检测到已安装的版本" "Yellow"
    Write-ColorText ""
    $choice = Read-Host "是否要重新安装？(y/N)"
    if ($choice -ne 'y' -and $choice -ne 'Y') {
        Write-ColorText "安装已取消" "Yellow"
        Pause-WithMessage "按任意键退出..."
        exit 0
    }
}

# 显示安装路径
Write-ColorText "安装路径: $InstallPath" "Cyan"
Write-ColorText ""
$customPath = Read-Host "是否使用自定义安装路径？(留空使用默认路径)"
if ($customPath) {
    $InstallPath = $customPath
    Write-ColorText "将安装到: $InstallPath" "Green"
}

Write-ColorText ""
Write-ColorText "准备开始安装..." "Green"
Write-ColorText ""

try {
    # 创建安装目录
    Write-ColorText "正在创建安装目录..." "Yellow"
    if (-not (Test-Path $InstallPath)) {
        New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    }
    Write-ColorText "✓ 安装目录创建成功" "Green"
    
    # 检查源文件
    $SourcePath = "publish-standalone"
    if (-not (Test-Path $SourcePath)) {
        Write-ColorText "❌ 错误: 找不到程序文件" "Red"
        Write-ColorText "请确保 publish-standalone 文件夹存在" "Red"
        Pause-WithMessage "按任意键退出..."
        exit 1
    }
    
    # 复制程序文件
    Write-ColorText "正在复制程序文件..." "Yellow"
    $fileCount = (Get-ChildItem $SourcePath -Recurse -File).Count
    $copiedCount = 0
    
    Get-ChildItem $SourcePath -Recurse | ForEach-Object {
        $relativePath = $_.FullName.Substring($SourcePath.Length + 1)
        $destPath = Join-Path $InstallPath $relativePath
        
        if ($_.PSIsContainer) {
            if (-not (Test-Path $destPath)) {
                New-Item -ItemType Directory -Path $destPath -Force | Out-Null
            }
        } else {
            $destDir = Split-Path $destPath -Parent
            if (-not (Test-Path $destDir)) {
                New-Item -ItemType Directory -Path $destDir -Force | Out-Null
            }
            Copy-Item $_.FullName $destPath -Force
            $copiedCount++
            if ($copiedCount % 50 -eq 0) {
                Write-Host "." -NoNewline -ForegroundColor Green
            }
        }
    }
    
    Write-ColorText ""
    Write-ColorText "✓ 程序文件复制完成 ($copiedCount 个文件)" "Green"
    
    # 创建快捷方式
    Write-ColorText "正在创建快捷方式..." "Yellow"
    
    $WshShell = New-Object -comObject WScript.Shell
    
    # 桌面快捷方式
    $DesktopShortcut = $WshShell.CreateShortcut("$env:PUBLIC\Desktop\我要记忆.lnk")
    $DesktopShortcut.TargetPath = "$InstallPath\MemoryEnhancer.exe"
    $DesktopShortcut.WorkingDirectory = $InstallPath
    if (Test-Path "$InstallPath\Resources\icon.ico") {
        $DesktopShortcut.IconLocation = "$InstallPath\Resources\icon.ico"
    }
    $DesktopShortcut.Description = "我要记忆 - Windows记忆增强程序"
    $DesktopShortcut.Save()
    
    # 开始菜单快捷方式
    $StartMenuShortcut = $WshShell.CreateShortcut("$env:ProgramData\Microsoft\Windows\Start Menu\Programs\我要记忆.lnk")
    $StartMenuShortcut.TargetPath = "$InstallPath\MemoryEnhancer.exe"
    $StartMenuShortcut.WorkingDirectory = $InstallPath
    if (Test-Path "$InstallPath\Resources\icon.ico") {
        $StartMenuShortcut.IconLocation = "$InstallPath\Resources\icon.ico"
    }
    $StartMenuShortcut.Description = "我要记忆 - Windows记忆增强程序"
    $StartMenuShortcut.Save()
    
    Write-ColorText "✓ 快捷方式创建成功" "Green"
    
    # 注册到系统
    Write-ColorText "正在注册程序到系统..." "Yellow"
    
    $UninstallKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\我要记忆"
    New-Item -Path $UninstallKey -Force | Out-Null
    Set-ItemProperty -Path $UninstallKey -Name "DisplayName" -Value "我要记忆"
    Set-ItemProperty -Path $UninstallKey -Name "DisplayVersion" -Value "1.0.0"
    Set-ItemProperty -Path $UninstallKey -Name "Publisher" -Value "周少文"
    Set-ItemProperty -Path $UninstallKey -Name "InstallLocation" -Value $InstallPath
    Set-ItemProperty -Path $UninstallKey -Name "UninstallString" -Value "PowerShell -ExecutionPolicy Bypass -File `"$InstallPath\Uninstall.ps1`""
    if (Test-Path "$InstallPath\Resources\icon.ico") {
        Set-ItemProperty -Path $UninstallKey -Name "DisplayIcon" -Value "$InstallPath\Resources\icon.ico"
    }
    Set-ItemProperty -Path $UninstallKey -Name "NoModify" -Value 1 -Type DWord
    Set-ItemProperty -Path $UninstallKey -Name "NoRepair" -Value 1 -Type DWord
    
    Write-ColorText "✓ 程序注册成功" "Green"
    
    # 创建卸载脚本
    Write-ColorText "正在创建卸载程序..." "Yellow"
    
    $UninstallScript = @"
# 我要记忆 - 卸载程序
param([string]`$InstallPath = "$InstallPath")

if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Start-Process PowerShell -Verb RunAs "-File `"`$PSCommandPath`" `$args"
    exit
}

Write-Host "我要记忆 - 卸载程序" -ForegroundColor Red
`$confirm = Read-Host "确定要卸载我要记忆吗？(y/N)"
if (`$confirm -ne 'y' -and `$confirm -ne 'Y') {
    exit 0
}

Write-Host "正在卸载..." -ForegroundColor Yellow

# 停止程序进程
Get-Process -Name "MemoryEnhancer" -ErrorAction SilentlyContinue | Stop-Process -Force

# 删除快捷方式
Remove-Item "`$env:PUBLIC\Desktop\我要记忆.lnk" -Force -ErrorAction SilentlyContinue
Remove-Item "`$env:ProgramData\Microsoft\Windows\Start Menu\Programs\我要记忆.lnk" -Force -ErrorAction SilentlyContinue

# 删除注册表项
Remove-Item "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\我要记忆" -Force -ErrorAction SilentlyContinue

# 删除程序文件
if (Test-Path `$InstallPath) {
    Remove-Item `$InstallPath -Recurse -Force
}

Write-Host "卸载完成" -ForegroundColor Green
Read-Host "按任意键退出"
"@
    
    $UninstallScript | Out-File -FilePath "$InstallPath\Uninstall.ps1" -Encoding UTF8
    
    Write-ColorText "✓ 卸载程序创建成功" "Green"
    Write-ColorText ""
    
    # 安装完成
    Write-ColorText "========================================" "Green"
    Write-ColorText "🎉 安装完成！" "Green"
    Write-ColorText "========================================" "Green"
    Write-ColorText ""
    Write-ColorText "程序已安装到: $InstallPath" "White"
    Write-ColorText "桌面快捷方式: 我要记忆.lnk" "White"
    Write-ColorText "开始菜单: 我要记忆" "White"
    Write-ColorText ""
    Write-ColorText "您可以通过以下方式启动程序:" "Cyan"
    Write-ColorText "• 双击桌面上的'我要记忆'图标" "White"
    Write-ColorText "• 从开始菜单搜索'我要记忆'" "White"
    Write-ColorText "• 直接运行: $InstallPath\MemoryEnhancer.exe" "White"
    Write-ColorText ""
    
    $runNow = Read-Host "是否现在运行程序？(Y/n)"
    if ($runNow -ne 'n' -and $runNow -ne 'N') {
        Write-ColorText "正在启动程序..." "Green"
        Start-Process "$InstallPath\MemoryEnhancer.exe"
    }
    
} catch {
    Write-ColorText ""
    Write-ColorText "❌ 安装失败" "Red"
    Write-ColorText "错误信息: $($_.Exception.Message)" "Red"
    Write-ColorText ""
    Write-ColorText "请检查:" "Yellow"
    Write-ColorText "• 是否有足够的磁盘空间" "White"
    Write-ColorText "• 是否有管理员权限" "White"
    Write-ColorText "• 安装路径是否可写" "White"
    Write-ColorText ""
    Pause-WithMessage "按任意键退出..."
    exit 1
}

Write-ColorText ""
Write-ColorText "感谢您使用我要记忆！" "Green"
Pause-WithMessage "按任意键退出..."
