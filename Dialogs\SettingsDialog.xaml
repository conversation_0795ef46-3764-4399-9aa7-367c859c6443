<Window x:Class="MemoryEnhancer.SettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MemoryEnhancer"
        mc:Ignorable="d"
        Title="设置" Height="550" Width="500" WindowStartupLocation="CenterScreen" Topmost="True">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0">
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="快捷键说明(_K)" Click="ShowKeyboardShortcuts_Click"/>
                <MenuItem Header="版本说明(_A)" Click="ShowAbout_Click"/>
            </MenuItem>
        </Menu>

        <Grid Grid.Row="1" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <TabControl>
                    <TabItem Header="基本设置">
                        <StackPanel>
                            <TextBlock Text="显示设置" FontWeight="Bold" Margin="0,10,0,5"/>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="40"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="间隔时间(秒):" VerticalAlignment="Center"/>
                                <Slider x:Name="intervalTimeSlider" Grid.Column="1" Minimum="0" Maximum="8"
                                        Value="3" TickFrequency="1" IsSnapToTickEnabled="True"/>
                                <TextBlock Grid.Column="2" Text="{Binding ElementName=intervalTimeSlider, Path=Value, StringFormat='{}{0:0}'}"
                                           VerticalAlignment="Center" HorizontalAlignment="Right"/>
                            </Grid>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="自动朗读:" VerticalAlignment="Center"/>
                                <CheckBox x:Name="autoReadCheckBox" Grid.Column="1" IsChecked="True" VerticalAlignment="Center"/>
                            </Grid>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="自动前进:" VerticalAlignment="Center"/>
                                <CheckBox x:Name="autoAdvanceCheckBox" Grid.Column="1" IsChecked="True" VerticalAlignment="Center"/>
                            </Grid>

                            <TextBlock Text="颜色设置" FontWeight="Bold" Margin="0,10,0,5"/>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="文字颜色:" VerticalAlignment="Center"/>
                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <Button x:Name="textColorButton" Click="TextColorButton_Click" Width="30" Height="20" Margin="0,0,10,0">
                                        <Rectangle x:Name="textColorRectangle" Width="20" Height="15" Fill="Black"/>
                                    </Button>
                                </StackPanel>
                            </Grid>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="背景颜色:" VerticalAlignment="Center"/>
                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <Button x:Name="bgColorButton" Click="BgColorButton_Click" Width="30" Height="20" Margin="0,0,10,0">
                                        <Rectangle x:Name="bgColorRectangle" Width="20" Height="15" Fill="White"/>
                                    </Button>
                                </StackPanel>
                            </Grid>

                            <TextBlock Text="学习设置" FontWeight="Bold" Margin="0,10,0,5"/>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="40"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="每日新内容数量:" VerticalAlignment="Center"/>
                                <Slider x:Name="newItemsSlider" Grid.Column="1" Minimum="0" Maximum="300"
                                        Value="20" TickFrequency="1" IsSnapToTickEnabled="True"/>
                                <TextBlock Grid.Column="2" Text="{Binding ElementName=newItemsSlider, Path=Value, StringFormat='{}{0:0}'}"
                                           VerticalAlignment="Center" HorizontalAlignment="Right"/>
                            </Grid>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="40"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="每日复习数量:" VerticalAlignment="Center"/>
                                <Slider x:Name="reviewItemsSlider" Grid.Column="1" Minimum="0" Maximum="300"
                                        Value="50" TickFrequency="1" IsSnapToTickEnabled="True"/>
                                <TextBlock Grid.Column="2" Text="{Binding ElementName=reviewItemsSlider, Path=Value, StringFormat='{}{0:0}'}"
                                           VerticalAlignment="Center" HorizontalAlignment="Right"/>
                            </Grid>

                            <TextBlock Text="文件组设置" FontWeight="Bold" Margin="0,10,0,5"/>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="当前文件组:" VerticalAlignment="Center"/>
                                <ComboBox x:Name="fileGroupComboBox" Grid.Column="1" Margin="0,2"
                                          SelectionChanged="FileGroupComboBox_SelectionChanged"/>
                            </Grid>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="仅今日项目:" VerticalAlignment="Center"/>
                                <CheckBox x:Name="todayOnlyCheckBox" Grid.Column="1" VerticalAlignment="Center"/>
                            </Grid>

                            <Button Content="导入内容" Click="ImportButton_Click" Margin="0,10" HorizontalAlignment="Left" Padding="10,5"/>
                        </StackPanel>
                    </TabItem>

                    <TabItem Header="字体设置">
                        <StackPanel Margin="5">
                            <TextBlock Text="字体样式" FontWeight="Bold" Margin="0,10,0,5"/>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="字体:" VerticalAlignment="Center"/>
                                <ComboBox x:Name="fontFamilyComboBox" Grid.Column="1" Margin="0,2"
                                          ItemsSource="{Binding Source={x:Static Fonts.SystemFontFamilies}}"
                                          SelectedValuePath="Source"
                                          DisplayMemberPath="Source"/>
                            </Grid>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="40"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="字体大小:" VerticalAlignment="Center"/>
                                <Slider x:Name="fontSizeSlider" Grid.Column="1" Minimum="8" Maximum="36"
                                        Value="14" TickFrequency="1" IsSnapToTickEnabled="True"/>
                                <TextBlock Grid.Column="2" Text="{Binding ElementName=fontSizeSlider, Path=Value, StringFormat='{}{0:0}'}"
                                           VerticalAlignment="Center" HorizontalAlignment="Right"/>
                            </Grid>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="粗体:" VerticalAlignment="Center"/>
                                <CheckBox x:Name="boldCheckBox" Grid.Column="1" VerticalAlignment="Center"/>
                            </Grid>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="斜体:" VerticalAlignment="Center"/>
                                <CheckBox x:Name="italicCheckBox" Grid.Column="1" VerticalAlignment="Center"/>
                            </Grid>

                            <Border Margin="0,10" Padding="10" BorderBrush="LightGray" BorderThickness="1" CornerRadius="5">
                                <TextBlock x:Name="fontPreviewText" Text="字体预览文本 Sample Text" TextWrapping="Wrap"/>
                            </Border>
                        </StackPanel>
                    </TabItem>

                    <TabItem Header="记忆算法">
                        <StackPanel Margin="5">
                            <TextBlock Text="算法选择" FontWeight="Bold" Margin="0,10,0,5"/>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="记忆算法:" VerticalAlignment="Center"/>
                                <ComboBox x:Name="algorithmComboBox" Grid.Column="1" Margin="0,2" SelectionChanged="AlgorithmComboBox_SelectionChanged">
                                    <ComboBoxItem Content="简单间隔重复" Tag="Simple"/>
                                    <ComboBoxItem Content="SuperMemo-2" Tag="SuperMemo2"/>
                                    <ComboBoxItem Content="SuperMemo-5" Tag="SuperMemo5"/>
                                </ComboBox>
                            </Grid>

                            <TextBlock x:Name="algorithmDescriptionText" TextWrapping="Wrap" Margin="0,10"
                                       Text="简单间隔重复算法：基于难度级别和复习次数指数增长间隔时间。"/>

                            <TextBlock Text="重复设置" FontWeight="Bold" Margin="0,20,0,5"/>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="40"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="单条重复次数:" VerticalAlignment="Center"/>
                                <Slider x:Name="repeatCountSlider" Grid.Column="1" Minimum="1" Maximum="30"
                                        Value="1" TickFrequency="1" IsSnapToTickEnabled="True"/>
                                <TextBlock Grid.Column="2" Text="{Binding ElementName=repeatCountSlider, Path=Value, StringFormat='{}{0:0}'}"
                                           VerticalAlignment="Center" HorizontalAlignment="Right"/>
                            </Grid>
                        </StackPanel>
                    </TabItem>

                    <TabItem Header="语音设置">
                        <StackPanel Margin="5">
                            <TextBlock Text="语音朗读设置" FontWeight="Bold" Margin="0,10,0,5"/>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="60"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="朗读速度:" VerticalAlignment="Center"/>
                                <Slider x:Name="speechRateSlider" Grid.Column="1" Minimum="-10" Maximum="10"
                                        Value="0" TickFrequency="1" IsSnapToTickEnabled="True"
                                        ValueChanged="SpeechRateSlider_ValueChanged"/>
                                <TextBlock Grid.Column="2" Text="{Binding ElementName=speechRateSlider, Path=Value, StringFormat='{}{0:0}'}"
                                           VerticalAlignment="Center" HorizontalAlignment="Right"/>
                            </Grid>

                            <TextBlock Text="速度说明：-10(最慢) ← 0(正常) → 10(最快)"
                                       FontSize="10" Foreground="Gray" Margin="120,0,0,10"/>

                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="智能停顿:" VerticalAlignment="Center"/>
                                <CheckBox x:Name="smartPauseCheckBox" Grid.Column="1" VerticalAlignment="Center"
                                          Checked="SmartPauseCheckBox_Changed" Unchecked="SmartPauseCheckBox_Changed"/>
                            </Grid>

                            <TextBlock Text="智能停顿说明：根据标点符号和空格自动调整朗读停顿时间"
                                       FontSize="10" Foreground="Gray" Margin="120,0,0,10" TextWrapping="Wrap"/>

                            <Button Content="测试语音" Click="TestSpeechButton_Click" Margin="0,10"
                                    HorizontalAlignment="Left" Padding="10,5"/>
                        </StackPanel>
                    </TabItem>

                    <TabItem Header="文件管理">
                        <StackPanel Margin="5">
                            <TextBlock Text="文件分组管理" FontWeight="Bold" Margin="0,10,0,5"/>

                            <ListView x:Name="fileGroupsListView" Margin="0,10" Height="200"
                                      BorderThickness="1" BorderBrush="LightGray" SelectionChanged="FileGroupsListView_SelectionChanged">
                                <ListView.View>
                                    <GridView>
                                        <GridViewColumn Header="文件组名称" Width="150" DisplayMemberBinding="{Binding Name}"/>
                                        <GridViewColumn Header="项目数量" Width="80" DisplayMemberBinding="{Binding ItemCount}"/>
                                        <GridViewColumn Header="导入日期" Width="120" DisplayMemberBinding="{Binding ImportDate, StringFormat='{}{0:yyyy-MM-dd}'}"/>
                                    </GridView>
                                </ListView.View>
                            </ListView>

                            <StackPanel Orientation="Horizontal" Margin="0,10">
                                <Button Content="重命名" Click="RenameFileGroup_Click" Margin="0,0,10,0" Padding="10,5"/>
                                <Button Content="删除" Click="DeleteFileGroup_Click" Margin="0,0,10,0" Padding="10,5"/>
                                <Button Content="刷新列表" Click="RefreshFileGroups_Click" Padding="10,5"/>
                            </StackPanel>
                        </StackPanel>
                    </TabItem>

                    <TabItem Header="统计信息">
                        <StackPanel Margin="5">
                            <TextBlock Text="学习统计" FontWeight="Bold" Margin="0,10,0,5"/>

                            <ListView x:Name="statisticsListView" Margin="0,10" Height="200"
                                      BorderThickness="1" BorderBrush="LightGray">
                                <ListView.View>
                                    <GridView>
                                        <GridViewColumn Header="统计项目" Width="150" DisplayMemberBinding="{Binding Key}"/>
                                        <GridViewColumn Header="数值" Width="150" DisplayMemberBinding="{Binding Value}"/>
                                    </GridView>
                                </ListView.View>
                            </ListView>

                            <Button Content="刷新统计信息" Click="RefreshStats_Click" Margin="0,10"
                                    HorizontalAlignment="Left" Padding="10,5"/>
                        </StackPanel>
                    </TabItem>
                </TabControl>
            </StackPanel>
        </ScrollViewer>

        </Grid>

        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="10,10,10,10">
            <Button Content="确定" Width="80" Click="OkButton_Click" Margin="0,0,10,0"/>
            <Button Content="取消" Width="80" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
