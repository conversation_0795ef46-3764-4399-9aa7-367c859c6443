# 我要记忆 - 自动安装程序创建脚本
param(
    [string]$OutputPath = "installer-output"
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "我要记忆 - 自动安装程序创建" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查发布文件夹
if (-not (Test-Path "publish-standalone")) {
    Write-Host "❌ 错误: 找不到 publish-standalone 文件夹" -ForegroundColor Red
    Write-Host ""
    Write-Host "正在自动创建发布版本..." -ForegroundColor Yellow
    
    try {
        & dotnet publish MemoryEnhancer.csproj --configuration Release --output publish-standalone --runtime win-x64 --self-contained true --verbosity minimal
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 发布版本创建成功" -ForegroundColor Green
        } else {
            Write-Host "❌ 发布版本创建失败" -ForegroundColor Red
            exit 1
        }
    }
    catch {
        Write-Host "❌ 发布版本创建失败: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# 创建输出目录
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath | Out-Null
}

Write-Host "正在创建安装程序..." -ForegroundColor Green
Write-Host ""

# 创建安装程序脚本
$InstallerScript = @'
# 我要记忆 - Windows 安装程序
# 此脚本需要管理员权限运行

param(
    [switch]$Uninstall = $false,
    [string]$InstallPath = "$env:ProgramFiles\我要记忆"
)

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "此安装程序需要管理员权限。正在重新启动..." -ForegroundColor Yellow
    Start-Process PowerShell -Verb RunAs "-File `"$PSCommandPath`" $args"
    exit
}

# 检查系统要求
$OSVersion = [System.Environment]::OSVersion.Version
if ($OSVersion.Major -lt 10 -or ($OSVersion.Major -eq 10 -and $OSVersion.Build -lt 17763)) {
    Write-Host "❌ 系统要求不满足" -ForegroundColor Red
    Write-Host "此程序需要 Windows 10 版本 1809 (Build 17763) 或更高版本" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

if (-not [Environment]::Is64BitOperatingSystem) {
    Write-Host "❌ 系统架构不支持" -ForegroundColor Red
    Write-Host "此程序需要 64 位系统" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

if ($Uninstall) {
    # 卸载程序
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "我要记忆 - 卸载程序" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host ""
    
    $confirm = Read-Host "确定要卸载我要记忆吗？(y/N)"
    if ($confirm -ne 'y' -and $confirm -ne 'Y') {
        Write-Host "卸载已取消" -ForegroundColor Yellow
        exit 0
    }
    
    Write-Host "正在卸载..." -ForegroundColor Yellow
    
    # 停止程序进程
    Get-Process -Name "MemoryEnhancer" -ErrorAction SilentlyContinue | Stop-Process -Force
    
    # 删除快捷方式
    $DesktopShortcut = "$env:PUBLIC\Desktop\我要记忆.lnk"
    $StartMenuShortcut = "$env:ProgramData\Microsoft\Windows\Start Menu\Programs\我要记忆.lnk"
    
    if (Test-Path $DesktopShortcut) { Remove-Item $DesktopShortcut -Force }
    if (Test-Path $StartMenuShortcut) { Remove-Item $StartMenuShortcut -Force }
    
    # 删除注册表项
    Remove-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\我要记忆" -ErrorAction SilentlyContinue
    Remove-Item -Path "HKLM:\SOFTWARE\我要记忆" -Recurse -ErrorAction SilentlyContinue
    
    # 删除程序文件
    if (Test-Path $InstallPath) {
        Remove-Item $InstallPath -Recurse -Force
    }
    
    Write-Host "✓ 卸载完成" -ForegroundColor Green
    Read-Host "按任意键退出"
    exit 0
}

# 安装程序
Write-Host "========================================" -ForegroundColor Green
Write-Host "我要记忆 - 安装程序" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "版本: 1.0.0" -ForegroundColor White
Write-Host "发布者: 周少文" -ForegroundColor White
Write-Host "系统要求: Windows 10+ (x64)" -ForegroundColor White
Write-Host ""

# 检查是否已安装
if (Test-Path "$InstallPath\MemoryEnhancer.exe") {
    Write-Host "检测到已安装的版本" -ForegroundColor Yellow
    $choice = Read-Host "是否要重新安装？(y/N)"
    if ($choice -ne 'y' -and $choice -ne 'Y') {
        Write-Host "安装已取消" -ForegroundColor Yellow
        exit 0
    }
}

Write-Host "安装路径: $InstallPath" -ForegroundColor Cyan
$customPath = Read-Host "是否使用自定义安装路径？(留空使用默认路径)"
if ($customPath) {
    $InstallPath = $customPath
}

Write-Host ""
Write-Host "正在安装到: $InstallPath" -ForegroundColor Green
Write-Host ""

try {
    # 创建安装目录
    if (-not (Test-Path $InstallPath)) {
        New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    }
    
    # 解压程序文件 (这里需要实际的文件复制逻辑)
    Write-Host "正在复制程序文件..." -ForegroundColor Yellow
    
    # 这里会被替换为实际的文件复制代码
    # PLACEHOLDER_FOR_FILE_COPY
    
    # 创建快捷方式
    Write-Host "正在创建快捷方式..." -ForegroundColor Yellow
    
    $WshShell = New-Object -comObject WScript.Shell
    
    # 桌面快捷方式
    $DesktopShortcut = $WshShell.CreateShortcut("$env:PUBLIC\Desktop\我要记忆.lnk")
    $DesktopShortcut.TargetPath = "$InstallPath\MemoryEnhancer.exe"
    $DesktopShortcut.WorkingDirectory = $InstallPath
    $DesktopShortcut.IconLocation = "$InstallPath\Resources\icon.ico"
    $DesktopShortcut.Description = "我要记忆 - Windows记忆增强程序"
    $DesktopShortcut.Save()
    
    # 开始菜单快捷方式
    $StartMenuShortcut = $WshShell.CreateShortcut("$env:ProgramData\Microsoft\Windows\Start Menu\Programs\我要记忆.lnk")
    $StartMenuShortcut.TargetPath = "$InstallPath\MemoryEnhancer.exe"
    $StartMenuShortcut.WorkingDirectory = $InstallPath
    $StartMenuShortcut.IconLocation = "$InstallPath\Resources\icon.ico"
    $StartMenuShortcut.Description = "我要记忆 - Windows记忆增强程序"
    $StartMenuShortcut.Save()
    
    # 注册到系统
    Write-Host "正在注册程序..." -ForegroundColor Yellow
    
    $UninstallKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\我要记忆"
    New-Item -Path $UninstallKey -Force | Out-Null
    Set-ItemProperty -Path $UninstallKey -Name "DisplayName" -Value "我要记忆"
    Set-ItemProperty -Path $UninstallKey -Name "DisplayVersion" -Value "1.0.0"
    Set-ItemProperty -Path $UninstallKey -Name "Publisher" -Value "周少文"
    Set-ItemProperty -Path $UninstallKey -Name "InstallLocation" -Value $InstallPath
    Set-ItemProperty -Path $UninstallKey -Name "UninstallString" -Value "PowerShell -ExecutionPolicy Bypass -File `"$InstallPath\Uninstall.ps1`""
    Set-ItemProperty -Path $UninstallKey -Name "DisplayIcon" -Value "$InstallPath\Resources\icon.ico"
    Set-ItemProperty -Path $UninstallKey -Name "NoModify" -Value 1 -Type DWord
    Set-ItemProperty -Path $UninstallKey -Name "NoRepair" -Value 1 -Type DWord
    
    # 创建卸载脚本
    $UninstallScript = $MyInvocation.MyCommand.Definition
    Copy-Item $UninstallScript "$InstallPath\Uninstall.ps1" -Force
    
    Write-Host ""
    Write-Host "✓ 安装完成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "程序已安装到: $InstallPath" -ForegroundColor White
    Write-Host "桌面快捷方式: 我要记忆.lnk" -ForegroundColor White
    Write-Host "开始菜单: 我要记忆" -ForegroundColor White
    Write-Host ""
    
    $runNow = Read-Host "是否现在运行程序？(Y/n)"
    if ($runNow -ne 'n' -and $runNow -ne 'N') {
        Start-Process "$InstallPath\MemoryEnhancer.exe"
    }
    
} catch {
    Write-Host "❌ 安装失败: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Read-Host "按任意键退出"
'@

# 保存安装程序脚本
$InstallerScriptPath = "$OutputPath\Install-MemoryEnhancer.ps1"
$InstallerScript | Out-File -FilePath $InstallerScriptPath -Encoding UTF8

Write-Host "✓ 安装程序脚本已创建: $InstallerScriptPath" -ForegroundColor Green

# 现在创建文件复制代码
Write-Host "正在生成文件复制代码..." -ForegroundColor Yellow

$FileCopyCode = @"
    # 复制主程序文件
    Copy-Item "PLACEHOLDER_SOURCE\MemoryEnhancer.exe" "`$InstallPath\" -Force
    Copy-Item "PLACEHOLDER_SOURCE\MemoryEnhancer.dll" "`$InstallPath\" -Force
    Copy-Item "PLACEHOLDER_SOURCE\*.json" "`$InstallPath\" -Force
    
    # 复制运行时库
    Get-ChildItem "PLACEHOLDER_SOURCE\*.dll" | Copy-Item -Destination "`$InstallPath\" -Force
    
    # 复制资源文件
    if (-not (Test-Path "`$InstallPath\Resources")) {
        New-Item -ItemType Directory -Path "`$InstallPath\Resources" -Force | Out-Null
    }
    Copy-Item "PLACEHOLDER_SOURCE\Resources\*" "`$InstallPath\Resources\" -Recurse -Force -ErrorAction SilentlyContinue
    
    # 复制语言文件
    if (Test-Path "PLACEHOLDER_SOURCE\zh-Hans") {
        Copy-Item "PLACEHOLDER_SOURCE\zh-Hans" "`$InstallPath\" -Recurse -Force
    }
    
    # 复制文档文件
    Copy-Item "PLACEHOLDER_SOURCE\README.md" "`$InstallPath\" -Force -ErrorAction SilentlyContinue
    Copy-Item "PLACEHOLDER_SOURCE\LICENSE.txt" "`$InstallPath\" -Force -ErrorAction SilentlyContinue
    Copy-Item "PLACEHOLDER_SOURCE\安装和使用说明.md" "`$InstallPath\" -Force -ErrorAction SilentlyContinue
"@

# 获取当前路径
$CurrentPath = (Get-Location).Path
$SourcePath = "$CurrentPath\publish-standalone"

# 替换占位符
$FileCopyCode = $FileCopyCode -replace "PLACEHOLDER_SOURCE", $SourcePath

# 更新安装程序脚本
$UpdatedScript = (Get-Content $InstallerScriptPath -Raw) -replace "    # PLACEHOLDER_FOR_FILE_COPY", $FileCopyCode
$UpdatedScript | Out-File -FilePath $InstallerScriptPath -Encoding UTF8

Write-Host "✓ 文件复制代码已生成" -ForegroundColor Green

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "安装程序创建完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "安装程序位置:" -ForegroundColor Cyan
Write-Host "  📄 $InstallerScriptPath" -ForegroundColor White
Write-Host ""
Write-Host "使用方法:" -ForegroundColor Cyan
Write-Host "  1. 将安装程序发送给用户" -ForegroundColor White
Write-Host "  2. 用户右键点击 -> '使用PowerShell运行'" -ForegroundColor White
Write-Host "  3. 或者在PowerShell中运行: .\Install-MemoryEnhancer.ps1" -ForegroundColor White
Write-Host ""
Write-Host "安装程序特性:" -ForegroundColor Cyan
Write-Host "  ✓ 自动检查系统要求 (Windows 10+ x64)" -ForegroundColor Green
Write-Host "  ✓ 需要管理员权限安装" -ForegroundColor Green
Write-Host "  ✓ 自动创建桌面和开始菜单快捷方式" -ForegroundColor Green
Write-Host "  ✓ 注册到系统程序列表" -ForegroundColor Green
Write-Host "  ✓ 包含完整的卸载功能" -ForegroundColor Green
Write-Host "  ✓ 自包含.NET运行时，无需额外安装" -ForegroundColor Green
Write-Host ""

Write-Host "现在您有了一个真正需要安装才能使用的Windows程序！" -ForegroundColor Green
Write-Host ""
