# 我要记忆

一个简单的Windows智能学习程序，帮助用户记忆和复习内容。

## 功能特点

- 单行显示记忆内容
- 自动调整宽度
- 可拖放窗口
- 支持多种格式文档导入（TXT、CSV、JSON）
- 定时显示
- 颜色选择
- 内容导航
- 自动朗读
- 难度标记
- 记忆规律算法（支持多种算法）
- 进度保存
- 统计信息（学习时间、复习次数）
- 自定义选项（字体大小、样式）

## 如何运行程序

### 方法一：直接运行编译好的程序

1. 下载最新的发布版本
2. 解压缩下载的文件
3. 双击运行 `MemoryEnhancer.exe`

### 方法二：从源代码编译运行

#### 前提条件

- 安装 [.NET 6.0 SDK](https://dotnet.microsoft.com/download/dotnet/6.0) 或更高版本
- 安装 [Visual Studio 2022](https://visualstudio.microsoft.com/vs/) 或 [Visual Studio Code](https://code.visualstudio.com/)

#### 使用Visual Studio 2022

1. 打开 `MemoryEnhancer.sln` 解决方案文件
2. 按 F5 键或点击"开始调试"按钮运行程序

#### 使用命令行

1. 打开命令提示符或PowerShell
2. 导航到项目根目录
3. 运行以下命令编译并运行程序：

```
dotnet build
dotnet run
```

或者直接生成可执行文件：

```
dotnet publish -c Release -r win-x64 --self-contained false
```

生成的可执行文件将位于 `bin\Release\net6.0-windows\win-x64\publish` 目录中。

## 使用说明

1. 首次运行程序时，需要通过设置导入记忆内容
2. 点击"设置"按钮，然后点击"导入内容"按钮选择要导入的文件
3. 导入成功后，程序将显示记忆内容
4. 使用界面上的按钮进行导航和操作：
   - 上一条/下一条：切换显示内容
   - 播放/暂停：控制自动播放
   - 朗读：朗读当前内容
   - 简单/困难：标记当前内容的难度
   - 设置：打开设置对话框
   - 关闭：关闭程序

## 注意事项

- 程序会自动保存进度和设置
- 所有数据保存在用户的AppData目录中
- 支持的导入文件格式：
  - TXT：每行一个记忆项
  - CSV：每行的第一列作为记忆项
  - JSON：字符串数组或包含content字段的对象数组
