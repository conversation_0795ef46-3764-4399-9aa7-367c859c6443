using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Documents;
using Microsoft.Win32;
using MemoryEnhancer.Utils;
using MemoryEnhancer.Dialogs;

namespace MemoryEnhancer
{
    public partial class SettingsDialog : Window
    {
        public UserSettings Settings { get; private set; }
        public bool SettingsChanged { get; private set; } = false;
        private Dictionary<string, string> algorithmDescriptions = new Dictionary<string, string>();

        public SettingsDialog(UserSettings currentSettings)
        {
            InitializeComponent();

            // 初始化算法描述
            InitializeAlgorithmDescriptions();

            // 复制当前设置，以便在用户取消时不影响原始设置
            Settings = new UserSettings
            {
                IntervalTimeInSeconds = currentSettings.IntervalTimeInSeconds,
                AutoRead = currentSettings.AutoRead,
                AutoAdvance = currentSettings.AutoAdvance,
                DailyNewItemsLimit = currentSettings.DailyNewItemsLimit,
                DailyReviewItemsLimit = currentSettings.DailyReviewItemsLimit,
                TextColor = new RGB(currentSettings.TextColor.R, currentSettings.TextColor.G, currentSettings.TextColor.B),
                BackgroundColor = new RGB(currentSettings.BackgroundColor.R, currentSettings.BackgroundColor.G, currentSettings.BackgroundColor.B),
                LastStudyDate = currentSettings.LastStudyDate,
                TodayNewItemsStudied = currentSettings.TodayNewItemsStudied,
                TodayReviewItemsStudied = currentSettings.TodayReviewItemsStudied,
                FontFamily = currentSettings.FontFamily,
                FontSize = currentSettings.FontSize,
                IsBold = currentSettings.IsBold,
                IsItalic = currentSettings.IsItalic,
                SelectedAlgorithm = currentSettings.SelectedAlgorithm,
                TotalStudyTime = currentSettings.TotalStudyTime,
                TotalItemsStudied = currentSettings.TotalItemsStudied,
                TotalReviewsCompleted = currentSettings.TotalReviewsCompleted,
                FirstStudyDate = currentSettings.FirstStudyDate,
                ConsecutiveDaysStudied = currentSettings.ConsecutiveDaysStudied,
                LongestStreak = currentSettings.LongestStreak,
                CurrentFileGroupId = currentSettings.CurrentFileGroupId,
                PlayTodayItemsOnly = currentSettings.PlayTodayItemsOnly
            };

            // 将设置应用到UI
            intervalTimeSlider.Value = Settings.IntervalTimeInSeconds;
            autoReadCheckBox.IsChecked = Settings.AutoRead;
            autoAdvanceCheckBox.IsChecked = Settings.AutoAdvance;
            newItemsSlider.Value = Settings.DailyNewItemsLimit;
            reviewItemsSlider.Value = Settings.DailyReviewItemsLimit;

            // 设置颜色
            textColorRectangle.Fill = new SolidColorBrush(Settings.GetTextColor());
            bgColorRectangle.Fill = new SolidColorBrush(Settings.GetBackgroundColor());

            // 设置字体
            fontFamilyComboBox.SelectedValue = Settings.FontFamily;
            fontSizeSlider.Value = Settings.FontSize;
            boldCheckBox.IsChecked = Settings.IsBold;
            italicCheckBox.IsChecked = Settings.IsItalic;

            // 更新字体预览
            UpdateFontPreview();

            // 设置算法选择
            switch (Settings.SelectedAlgorithm)
            {
                case RepetitionAlgorithm.Simple:
                    algorithmComboBox.SelectedIndex = 0;
                    break;
                case RepetitionAlgorithm.SuperMemo2:
                    algorithmComboBox.SelectedIndex = 1;
                    break;
                case RepetitionAlgorithm.SuperMemo5:
                    algorithmComboBox.SelectedIndex = 2;
                    break;
            }

            // 设置重复次数
            repeatCountSlider.Value = Settings.RepeatCount;

            // 设置文件组选择
            LoadFileGroupsList();
            todayOnlyCheckBox.IsChecked = Settings.PlayTodayItemsOnly;

            // 加载统计信息
            LoadStatistics();

            // 加载文件组列表
            LoadFileGroups();

            // 添加字体设置变更事件
            fontFamilyComboBox.SelectionChanged += FontSettings_Changed;
            fontSizeSlider.ValueChanged += FontSettings_Changed;
            boldCheckBox.Checked += FontSettings_Changed;
            boldCheckBox.Unchecked += FontSettings_Changed;
            italicCheckBox.Checked += FontSettings_Changed;
            italicCheckBox.Unchecked += FontSettings_Changed;
        }

        private void InitializeAlgorithmDescriptions()
        {
            algorithmDescriptions.Add("Simple", "简单间隔重复算法：基于难度级别和复习次数指数增长间隔时间。简单易用，适合初学者。");
            algorithmDescriptions.Add("SuperMemo2", "SuperMemo-2算法：第二代SuperMemo算法，使用固定的间隔序列（1天、6天）开始，然后根据难度系数调整间隔。");
            algorithmDescriptions.Add("SuperMemo5", "SuperMemo-5算法：第五代SuperMemo算法，使用更复杂的公式计算难度系数和间隔，能更精确地适应个人记忆特点。");
        }

        private void LoadStatistics()
        {
            var stats = MemoryManager.GetStatisticsSummary(Settings);
            statisticsListView.ItemsSource = stats.Select(kvp => new { Key = kvp.Key, Value = kvp.Value });
        }

        private void LoadFileGroups()
        {
            var fileGroups = MemoryManager.LoadFileGroups();
            var displayItems = fileGroups.Select(g => new
            {
                Id = g.Id,
                Name = g.Name,
                ItemCount = g.Items.Count,
                ImportDate = g.ImportDate
            }).ToList();

            fileGroupsListView.ItemsSource = displayItems;
        }

        private void LoadFileGroupsList()
        {
            var fileGroups = MemoryManager.LoadFileGroups();

            // 清空现有项目
            fileGroupComboBox.Items.Clear();

            // 添加"所有项目"选项（用于兼容旧的统一列表）
            fileGroupComboBox.Items.Add(new ComboBoxItem
            {
                Content = "所有项目（旧版）",
                Tag = ""
            });

            // 添加文件组
            foreach (var group in fileGroups)
            {
                var item = new ComboBoxItem
                {
                    Content = $"{group.Name} ({group.GetStatistics()})",
                    Tag = group.Id
                };
                fileGroupComboBox.Items.Add(item);
            }

            // 选择当前文件组
            SelectCurrentFileGroup();
        }

        private void SelectCurrentFileGroup()
        {
            if (string.IsNullOrEmpty(Settings.CurrentFileGroupId))
            {
                // 选择"所有项目"
                fileGroupComboBox.SelectedIndex = 0;
            }
            else
            {
                // 查找并选择对应的文件组
                for (int i = 0; i < fileGroupComboBox.Items.Count; i++)
                {
                    if (fileGroupComboBox.Items[i] is ComboBoxItem item &&
                        item.Tag?.ToString() == Settings.CurrentFileGroupId)
                    {
                        fileGroupComboBox.SelectedIndex = i;
                        return;
                    }
                }
                // 如果没找到，默认选择第一个
                fileGroupComboBox.SelectedIndex = 0;
            }
        }

        private void FileGroupComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (fileGroupComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var groupId = selectedItem.Tag?.ToString() ?? "";
                Settings.CurrentFileGroupId = groupId;
                SettingsChanged = true;
            }
        }

        private void FileGroupsListView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 可以在这里添加选择变更的处理逻辑
        }

        private void RenameFileGroup_Click(object sender, RoutedEventArgs e)
        {
            if (fileGroupsListView.SelectedItem != null)
            {
                dynamic selectedItem = fileGroupsListView.SelectedItem;
                string groupId = selectedItem.Id;
                string currentName = selectedItem.Name;

                var inputDialog = new InputDialog("重命名文件组", "请输入新的文件组名称:", currentName);
                inputDialog.Owner = this;

                if (inputDialog.ShowDialog() == true && !string.IsNullOrWhiteSpace(inputDialog.InputText))
                {
                    if (MemoryManager.RenameFileGroup(groupId, inputDialog.InputText))
                    {
                        LoadFileGroups(); // 刷新列表
                        SettingsChanged = true; // 标记设置已更改
                        MessageBox.Show("文件组重命名成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            else
            {
                MessageBox.Show("请先选择要重命名的文件组。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeleteFileGroup_Click(object sender, RoutedEventArgs e)
        {
            if (fileGroupsListView.SelectedItem != null)
            {
                dynamic selectedItem = fileGroupsListView.SelectedItem;
                string groupId = selectedItem.Id;
                string groupName = selectedItem.Name;

                var result = MessageBox.Show($"确定要删除文件组 '{groupName}' 吗？\n这将删除该文件组中的所有内容，此操作不可撤销。",
                                           "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    if (MemoryManager.DeleteFileGroup(groupId))
                    {
                        // 如果删除的是当前选择的文件组，清空当前选择
                        if (Settings.CurrentFileGroupId == groupId)
                        {
                            Settings.CurrentFileGroupId = string.Empty;
                        }

                        LoadFileGroups(); // 刷新列表
                        SettingsChanged = true; // 标记设置已更改
                        MessageBox.Show("文件组删除成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            else
            {
                MessageBox.Show("请先选择要删除的文件组。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void RefreshFileGroups_Click(object sender, RoutedEventArgs e)
        {
            LoadFileGroups();
        }

        private void UpdateFontPreview()
        {
            try
            {
                FontFamily fontFamily = new FontFamily(fontFamilyComboBox.SelectedValue?.ToString() ?? Settings.FontFamily);
                double fontSize = fontSizeSlider.Value;
                FontWeight fontWeight = boldCheckBox.IsChecked == true ? FontWeights.Bold : FontWeights.Normal;
                FontStyle fontStyle = italicCheckBox.IsChecked == true ? FontStyles.Italic : FontStyles.Normal;

                fontPreviewText.FontFamily = fontFamily;
                fontPreviewText.FontSize = fontSize;
                fontPreviewText.FontWeight = fontWeight;
                fontPreviewText.FontStyle = fontStyle;
            }
            catch (Exception)
            {
                // 如果字体设置出错，使用默认字体
                fontPreviewText.FontFamily = new FontFamily("Microsoft YaHei UI");
                fontPreviewText.FontSize = 14;
                fontPreviewText.FontWeight = FontWeights.Normal;
                fontPreviewText.FontStyle = FontStyles.Normal;
            }
        }

        private void FontSettings_Changed(object sender, RoutedEventArgs e)
        {
            UpdateFontPreview();
        }

        private void AlgorithmComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (algorithmComboBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag != null)
            {
                string? algorithmKey = selectedItem.Tag.ToString();
                if (algorithmKey != null && algorithmDescriptions.ContainsKey(algorithmKey))
                {
                    algorithmDescriptionText.Text = algorithmDescriptions[algorithmKey];
                }
            }
        }

        private void RefreshStats_Click(object sender, RoutedEventArgs e)
        {
            LoadStatistics();
        }

        private void TextColorButton_Click(object sender, RoutedEventArgs e)
        {
            // 使用WPF原生的颜色选择器（简单实现）
            var colorDialog = new ColorPickerDialog();
            var currentColor = Settings.GetTextColor();
            colorDialog.SelectedColor = Color.FromRgb(currentColor.R, currentColor.G, currentColor.B);
            colorDialog.Owner = this;

            if (colorDialog.ShowDialog() == true)
            {
                var newColor = colorDialog.SelectedColor;
                Settings.TextColor = new RGB(newColor.R, newColor.G, newColor.B);
                textColorRectangle.Fill = new SolidColorBrush(newColor);
            }
        }

        private void BgColorButton_Click(object sender, RoutedEventArgs e)
        {
            // 使用WPF原生的颜色选择器（简单实现）
            var colorDialog = new ColorPickerDialog();
            var currentColor = Settings.GetBackgroundColor();
            colorDialog.SelectedColor = Color.FromRgb(currentColor.R, currentColor.G, currentColor.B);
            colorDialog.Owner = this;

            if (colorDialog.ShowDialog() == true)
            {
                var newColor = colorDialog.SelectedColor;
                Settings.BackgroundColor = new RGB(newColor.R, newColor.G, newColor.B);
                bgColorRectangle.Fill = new SolidColorBrush(newColor);
            }
        }

        private void ImportButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "所有支持的文件|*.txt;*.csv;*.json;*.xlsx;*.xls;*.docx;*.md;*.markdown|文本文件 (*.txt)|*.txt|CSV文件 (*.csv)|*.csv|JSON文件 (*.json)|*.json|Excel文件 (*.xlsx;*.xls)|*.xlsx;*.xls|Word文件 (*.docx)|*.docx|Markdown文件 (*.md;*.markdown)|*.md;*.markdown|所有文件 (*.*)|*.*",
                Title = "选择要导入的文件",
                FilterIndex = 1  // 默认选择"所有支持的文件"
            };

            // 确保文件对话框在当前窗口上方显示
            if (openFileDialog.ShowDialog(this) == true)
            {
                try
                {
                    // 询问用户是否要为导入的文件自定义名称
                    var fileName = System.IO.Path.GetFileNameWithoutExtension(openFileDialog.FileName);
                    var inputDialog = new InputDialog("文件组名称", $"请输入文件组名称:", fileName);
                    inputDialog.Owner = this;

                    string? groupName = null;
                    if (inputDialog.ShowDialog() == true)
                    {
                        groupName = inputDialog.InputText;
                    }
                    else
                    {
                        // 用户取消了，使用默认名称
                        groupName = fileName;
                    }

                    // 使用新的文件分组导入方法
                    string? groupId = MemoryManager.ImportFileAsGroup(openFileDialog.FileName, groupName);

                    if (!string.IsNullOrEmpty(groupId))
                    {
                        var fileGroup = MemoryManager.GetFileGroupById(groupId);
                        if (fileGroup != null)
                        {
                            MessageBox.Show($"文件组 '{fileGroup.Name}' 导入成功！共导入 {fileGroup.Items.Count} 个项目。",
                                          "导入完成", MessageBoxButton.OK, MessageBoxImage.Information);

                            // 设置当前文件组为新导入的文件组
                            Settings.CurrentFileGroupId = groupId;

                            // 刷新文件组列表
                            LoadFileGroupsList();
                            LoadFileGroups(); // 刷新文件管理标签页的列表

                            // 设置标志，表示内容已更新
                            SettingsChanged = true;
                        }
                    }
                    else
                    {
                        MessageBox.Show("导入失败，请检查文件格式。", "导入失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导入过程中发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            // 从UI更新设置
            Settings.IntervalTimeInSeconds = (int)intervalTimeSlider.Value;
            Settings.AutoRead = autoReadCheckBox.IsChecked ?? true;
            Settings.AutoAdvance = autoAdvanceCheckBox.IsChecked ?? true;
            Settings.DailyNewItemsLimit = (int)newItemsSlider.Value;
            Settings.DailyReviewItemsLimit = (int)reviewItemsSlider.Value;

            // 更新字体设置
            Settings.FontFamily = fontFamilyComboBox.SelectedValue?.ToString() ?? "Microsoft YaHei UI";
            Settings.FontSize = fontSizeSlider.Value;
            Settings.IsBold = boldCheckBox.IsChecked ?? false;
            Settings.IsItalic = italicCheckBox.IsChecked ?? false;

            // 更新算法设置
            if (algorithmComboBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag != null)
            {
                string? algorithmKey = selectedItem.Tag.ToString();
                if (algorithmKey != null)
                {
                    switch (algorithmKey)
                    {
                        case "Simple":
                            Settings.SelectedAlgorithm = RepetitionAlgorithm.Simple;
                            break;
                        case "SuperMemo2":
                            Settings.SelectedAlgorithm = RepetitionAlgorithm.SuperMemo2;
                            break;
                        case "SuperMemo5":
                            Settings.SelectedAlgorithm = RepetitionAlgorithm.SuperMemo5;
                            break;
                    }
                }
            }

            // 更新文件组设置
            Settings.PlayTodayItemsOnly = todayOnlyCheckBox.IsChecked ?? true;

            // 更新重复次数设置
            Settings.RepeatCount = (int)repeatCountSlider.Value;

            SettingsChanged = true;
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void ShowKeyboardShortcuts_Click(object sender, RoutedEventArgs e)
        {
            var shortcutsMessage = @"键盘快捷键说明：

基本操作：
• 左箭头 / A键：上一条内容
• 右箭头 / D键：下一条内容
• 空格键：播放/暂停自动播放
• R键：朗读/停止朗读当前内容

学习操作：
• E键：标记当前内容为简单
• H键：标记当前内容为困难

系统操作：
• S键：打开设置对话框
• Ctrl+S：立即保存所有数据
• F1键：显示快捷键帮助
• ESC键：关闭程序

提示：
- 使用键盘快捷键可以提高学习效率
- 在朗读过程中，程序会等待朗读完成后再自动前进
- 标记难度会影响下次复习的时间间隔";

            MessageBox.Show(shortcutsMessage, "键盘快捷键说明", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ShowAbout_Click(object sender, RoutedEventArgs e)
        {
            var aboutMessage = @"我要记忆 - Windows记忆增强程序

程序信息：
• 文件名：我要记忆
• 作者：周少文
• 版本：1.0.0
• 开发日期：2024年12月
• 最后更新：2024年12月

功能特色：
• 智能间隔重复算法
• 多文件格式支持（TXT、CSV、Excel、Word等）
• 自动朗读功能
• 文件分组管理
• 学习进度统计
• 自定义界面设置

技术支持：
• 基于SuperMemo算法的记忆优化
• 支持多种记忆算法选择
• 完整的学习数据统计
• 可自定义的学习参数

感谢您使用我要记忆程序！";

            MessageBox.Show(aboutMessage, "关于 - 我要记忆", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
