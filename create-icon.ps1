# PowerShell Script: Create Application Icon
# This script creates a simple ICO file for the application

Write-Host "Creating application icon..." -ForegroundColor Green

# Create Resources directory
$resourcesDir = "Resources"
if (!(Test-Path $resourcesDir)) {
    New-Item -ItemType Directory -Path $resourcesDir -Force
    Write-Host "Created Resources directory" -ForegroundColor Yellow
}

# 检查是否有.NET的System.Drawing可用
try {
    Add-Type -AssemblyName System.Drawing
    Write-Host "System.Drawing程序集加载成功" -ForegroundColor Green

    # 创建一个简单的图标位图
    $bitmap = New-Object System.Drawing.Bitmap(256, 256)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)

    # 设置高质量渲染
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic

    # 创建渐变背景
    $rect = New-Object System.Drawing.Rectangle(20, 20, 216, 216)
    $brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush($rect, [System.Drawing.Color]::FromArgb(74, 144, 226), [System.Drawing.Color]::FromArgb(46, 95, 138), [System.Drawing.Drawing2D.LinearGradientMode]::Diagonal)
    $graphics.FillEllipse($brush, $rect)

    # 绘制大脑轮廓
    $pen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, 4)

    # 左脑半球
    $leftBrain = New-Object System.Drawing.Rectangle(40, 80, 80, 96)
    $graphics.DrawEllipse($pen, $leftBrain)

    # 右脑半球
    $rightBrain = New-Object System.Drawing.Rectangle(136, 80, 80, 96)
    $graphics.DrawEllipse($pen, $rightBrain)

    # 中央分割线
    $graphics.DrawLine($pen, 128, 80, 128, 176)

    # 绘制灯泡（记忆符号）
    $lightBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 215, 0))
    $lightRect = New-Object System.Drawing.Rectangle(116, 40, 24, 30)
    $graphics.FillEllipse($lightBrush, $lightRect)

    # 灯泡底座
    $baseBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(224, 224, 224))
    $baseRect = New-Object System.Drawing.Rectangle(120, 68, 16, 8)
    $graphics.FillRectangle($baseBrush, $baseRect)

    # 添加一些记忆点
    $dotBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 165, 0))
    $graphics.FillEllipse($dotBrush, 70, 100, 8, 8)
    $graphics.FillEllipse($dotBrush, 178, 100, 8, 8)
    $graphics.FillEllipse($dotBrush, 90, 140, 6, 6)
    $graphics.FillEllipse($dotBrush, 160, 140, 6, 6)

    # 清理资源
    $graphics.Dispose()
    $brush.Dispose()
    $pen.Dispose()
    $lightBrush.Dispose()
    $baseBrush.Dispose()
    $dotBrush.Dispose()

    # 保存为PNG（因为直接创建ICO比较复杂）
    $pngPath = "$resourcesDir\icon.png"
    $bitmap.Save($pngPath, [System.Drawing.Imaging.ImageFormat]::Png)
    Write-Host "PNG图标已保存到: $pngPath" -ForegroundColor Green

    # 创建不同尺寸的图标
    $sizes = @(16, 32, 48, 64, 128, 256)
    foreach ($size in $sizes) {
        $resizedBitmap = New-Object System.Drawing.Bitmap($size, $size)
        $resizedGraphics = [System.Drawing.Graphics]::FromImage($resizedBitmap)
        $resizedGraphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $resizedGraphics.DrawImage($bitmap, 0, 0, $size, $size)

        $resizedPath = "$resourcesDir\icon_$size.png"
        $resizedBitmap.Save($resizedPath, [System.Drawing.Imaging.ImageFormat]::Png)

        $resizedGraphics.Dispose()
        $resizedBitmap.Dispose()
    }

    $bitmap.Dispose()
    Write-Host "已创建多种尺寸的图标文件" -ForegroundColor Green

} catch {
    Write-Host "无法使用System.Drawing创建图标: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动创建图标文件或使用在线图标生成工具" -ForegroundColor Yellow
}

# 提供手动创建图标的说明
Write-Host "`n=== 图标创建说明 ===" -ForegroundColor Cyan
Write-Host "1. 如果自动创建失败，请访问以下在线工具创建图标：" -ForegroundColor White
Write-Host "   - https://www.favicon-generator.org/" -ForegroundColor Blue
Write-Host "   - https://convertio.co/png-ico/" -ForegroundColor Blue
Write-Host "   - https://www.icoconverter.com/" -ForegroundColor Blue
Write-Host "`n2. 图标设计建议：" -ForegroundColor White
Write-Host "   - 主题：大脑/记忆/学习相关" -ForegroundColor Gray
Write-Host "   - 颜色：蓝色系（智慧）+ 金色（灵感）" -ForegroundColor Gray
Write-Host "   - 尺寸：256x256像素，支持透明背景" -ForegroundColor Gray
Write-Host "   - 格式：ICO文件，包含多种尺寸" -ForegroundColor Gray
Write-Host "`n3. 将创建的icon.ico文件放在Resources目录中" -ForegroundColor White

Write-Host "`n图标创建脚本执行完成！" -ForegroundColor Green
