我要记忆 v1.0 - 安装包使用说明
========================================

📦 安装包类型
----------------------------------------

1. 便携版
   文件名: 我要记忆_v1.0_便携版.zip (约 88MB)
   使用方法: 解压后直接运行 MemoryEnhancer.exe
   优点: 无需安装，解压即用
   缺点: 不会集成到系统中

2. 安装版 (推荐)
   文件名: 我要记忆_v1.0_安装版_修复版.zip (约 88MB)
   使用方法:
     1. 解压 我要记忆_v1.0_安装版_修复版.zip
     2. 双击运行 安装程序.bat
     3. 按照提示完成安装
   优点: 完整的系统集成，创建快捷方式，支持卸载

   备用方法:
     1. 解压后右键点击 Install-MemoryEnhancer-Fixed.ps1
     2. 选择 "使用PowerShell运行"

🎯 系统要求
----------------------------------------
- Windows 10 版本 1809 (Build 17763) 或更高
- 64位系统
- 管理员权限 (仅安装版需要)

✅ 安装版特性
----------------------------------------
- ✓ 需要管理员权限安装
- ✓ 安装到 Program Files 目录
- ✓ 创建桌面快捷方式
- ✓ 创建开始菜单快捷方式
- ✓ 注册到系统程序列表
- ✓ 支持通过控制面板卸载
- ✓ 自包含 .NET 运行时

🚀 安装版详细步骤
----------------------------------------
方法一 (推荐):
1. 解压 我要记忆_v1.0_安装版_修复版.zip 到任意文件夹
2. 双击运行 安装程序.bat
3. 如果出现安全提示，选择"是"或"允许"
4. 按照安装向导提示完成安装
5. 安装完成后可以从桌面或开始菜单启动程序

方法二 (备用):
1. 解压 我要记忆_v1.0_安装版_修复版.zip 到任意文件夹
2. 右键点击 Install-MemoryEnhancer-Fixed.ps1
3. 选择 "使用PowerShell运行"
4. 按照安装向导提示完成安装

📱 便携版详细步骤
----------------------------------------
1. 解压 我要记忆_v1.0_便携版.zip 到任意文件夹
2. 进入解压后的文件夹
3. 双击 MemoryEnhancer.exe 运行程序

🔧 故障排除
----------------------------------------
如果安装版无法运行：
1. 确保以管理员权限运行PowerShell
2. 检查系统是否为Windows 10+ 64位
3. 确保PowerShell执行策略允许运行脚本

如果便携版无法运行：
1. 检查系统是否为Windows 10+ 64位
2. 确保解压完整，没有缺失文件
3. 尝试右键以管理员身份运行

🎉 使用建议
----------------------------------------
推荐使用安装版，因为它提供完整的Windows程序体验：
- 系统集成更好
- 可以通过控制面板卸载
- 有桌面和开始菜单快捷方式
- 更符合Windows程序标准

如果您需要便携使用或不想安装到系统，可以选择便携版。

📞 技术支持
----------------------------------------
如果遇到问题，请检查：
1. 系统要求是否满足
2. 是否有足够的磁盘空间
3. 是否有管理员权限
4. Windows事件查看器中的错误信息

========================================
祝您使用愉快！
