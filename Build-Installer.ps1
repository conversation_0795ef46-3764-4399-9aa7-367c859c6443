# 我要记忆 - 安装程序构建脚本 (PowerShell版本)
param(
    [string]$BuildTool = "auto",  # auto, nsis, inno, wix
    [switch]$Force = $false
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "我要记忆 - 安装程序构建脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查发布文件夹
if (-not (Test-Path "publish-standalone")) {
    Write-Host "❌ 错误: 找不到 publish-standalone 文件夹" -ForegroundColor Red
    Write-Host ""
    Write-Host "请先运行以下命令创建发布版本:" -ForegroundColor Yellow
    Write-Host "dotnet publish MemoryEnhancer.csproj --configuration Release --output publish-standalone --runtime win-x64 --self-contained true" -ForegroundColor White
    Write-Host ""
    Read-Host "按任意键退出"
    exit 1
}

# 创建输出目录
if (-not (Test-Path "installer-output")) {
    New-Item -ItemType Directory -Path "installer-output" | Out-Null
}

Write-Host "正在检查安装程序构建工具..." -ForegroundColor Green
Write-Host ""

# 检查工具函数
function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# 构建结果
$BuildSuccess = $false
$OutputFiles = @()

# 方法1: NSIS
if ($BuildTool -eq "auto" -or $BuildTool -eq "nsis") {
    Write-Host "[1/3] 检查 NSIS..." -ForegroundColor Blue
    
    if (Test-Command "makensis") {
        Write-Host "✓ 找到 NSIS，正在构建安装程序..." -ForegroundColor Green
        
        try {
            & makensis "installer.nsi"
            
            if (Test-Path "我要记忆_v1.0_Windows10+_安装程序.exe") {
                Move-Item "我要记忆_v1.0_Windows10+_安装程序.exe" "installer-output\" -Force
                Write-Host "✓ NSIS 安装程序构建成功" -ForegroundColor Green
                $OutputFiles += "installer-output\我要记忆_v1.0_Windows10+_安装程序.exe"
                $BuildSuccess = $true
                
                if ($BuildTool -eq "nsis") { return }
            }
            else {
                Write-Host "✗ NSIS 构建失败" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "✗ NSIS 构建出错: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    else {
        Write-Host "✗ 未找到 NSIS" -ForegroundColor Yellow
    }
    
    Write-Host ""
}

# 方法2: Inno Setup
if ($BuildTool -eq "auto" -or $BuildTool -eq "inno") {
    Write-Host "[2/3] 检查 Inno Setup..." -ForegroundColor Blue
    
    if (Test-Command "iscc") {
        Write-Host "✓ 找到 Inno Setup，正在构建安装程序..." -ForegroundColor Green
        
        try {
            & iscc "installer-setup.iss"
            
            if (Test-Path "installer-output\我要记忆_v1.0_Windows10+_安装版.exe") {
                Write-Host "✓ Inno Setup 安装程序构建成功" -ForegroundColor Green
                $OutputFiles += "installer-output\我要记忆_v1.0_Windows10+_安装版.exe"
                $BuildSuccess = $true
                
                if ($BuildTool -eq "inno") { return }
            }
            else {
                Write-Host "✗ Inno Setup 构建失败" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "✗ Inno Setup 构建出错: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    else {
        Write-Host "✗ 未找到 Inno Setup" -ForegroundColor Yellow
    }
    
    Write-Host ""
}

# 方法3: WiX Toolset
if ($BuildTool -eq "auto" -or $BuildTool -eq "wix") {
    Write-Host "[3/3] 检查 WiX Toolset..." -ForegroundColor Blue
    
    if (Test-Command "candle") {
        Write-Host "✓ 找到 WiX Toolset，正在构建 MSI 安装包..." -ForegroundColor Green
        
        try {
            # 编译 WiX 源文件
            & candle "installer.wxs" -out "installer-output\installer.wixobj"
            
            if ($LASTEXITCODE -eq 0) {
                # 链接生成 MSI
                & light "installer-output\installer.wixobj" -out "installer-output\我要记忆_v1.0_Windows10+.msi" -ext "WixUIExtension"
                
                if ($LASTEXITCODE -eq 0 -and (Test-Path "installer-output\我要记忆_v1.0_Windows10+.msi")) {
                    Write-Host "✓ WiX MSI 安装包构建成功" -ForegroundColor Green
                    $OutputFiles += "installer-output\我要记忆_v1.0_Windows10+.msi"
                    $BuildSuccess = $true
                    
                    # 清理临时文件
                    Remove-Item "installer-output\installer.wixobj" -ErrorAction SilentlyContinue
                    
                    if ($BuildTool -eq "wix") { return }
                }
                else {
                    Write-Host "✗ WiX 链接失败" -ForegroundColor Red
                }
            }
            else {
                Write-Host "✗ WiX 编译失败" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "✗ WiX 构建出错: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    else {
        Write-Host "✗ 未找到 WiX Toolset" -ForegroundColor Yellow
    }
    
    Write-Host ""
}

# 显示结果
if ($BuildSuccess) {
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "构建成功!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "安装程序已生成:" -ForegroundColor Green
    
    foreach ($file in $OutputFiles) {
        if (Test-Path $file) {
            $fileInfo = Get-Item $file
            $sizeInMB = [math]::Round($fileInfo.Length / 1MB, 2)
            Write-Host "  📦 $($fileInfo.Name) ($sizeInMB MB)" -ForegroundColor White
        }
    }
    
    Write-Host ""
    Write-Host "安装程序特性:" -ForegroundColor Cyan
    Write-Host "  ✓ 需要管理员权限安装" -ForegroundColor White
    Write-Host "  ✓ 支持 Windows 10+ (x64)" -ForegroundColor White
    Write-Host "  ✓ 自包含 .NET 运行时" -ForegroundColor White
    Write-Host "  ✓ 创建开始菜单快捷方式" -ForegroundColor White
    Write-Host "  ✓ 可选桌面快捷方式" -ForegroundColor White
    Write-Host "  ✓ 文件关联支持" -ForegroundColor White
    Write-Host "  ✓ 完整的卸载支持" -ForegroundColor White
    Write-Host ""
    Write-Host "您现在可以分发这些安装程序给用户使用。" -ForegroundColor Green
}
else {
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "构建失败: 未找到可用的安装程序构建工具" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host ""
    Write-Host "请安装以下工具之一:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "1. NSIS (推荐)" -ForegroundColor Cyan
    Write-Host "   下载地址: https://nsis.sourceforge.io/Download" -ForegroundColor White
    Write-Host "   安装后将安装目录添加到 PATH 环境变量" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. Inno Setup" -ForegroundColor Cyan
    Write-Host "   下载地址: https://jrsoftware.org/isdl.php" -ForegroundColor White
    Write-Host "   安装后将安装目录添加到 PATH 环境变量" -ForegroundColor Gray
    Write-Host ""
    Write-Host "3. WiX Toolset" -ForegroundColor Cyan
    Write-Host "   下载地址: https://wixtoolset.org/releases/" -ForegroundColor White
    Write-Host "   或使用: dotnet tool install --global wix" -ForegroundColor Gray
    Write-Host ""
    Write-Host "安装完成后重新运行此脚本。" -ForegroundColor Yellow
    Write-Host ""
    
    exit 1
}

Write-Host ""
Read-Host "按任意键退出"
