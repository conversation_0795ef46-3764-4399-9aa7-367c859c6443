; 我要记忆 NSIS 安装脚本
; 编译器版本要求: NSIS 3.0+

;--------------------------------
; 包含现代UI
!include "MUI2.nsh"
!include "x64.nsh"
!include "WinVer.nsh"

;--------------------------------
; 基本信息
!define PRODUCT_NAME "我要记忆"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "周少文"
!define PRODUCT_WEB_SITE "https://github.com/memory-enhancer"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\MemoryEnhancer.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"
!define PRODUCT_STARTMENU_REGVAL "NSIS:StartMenuDir"

;--------------------------------
; 安装程序属性
Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "我要记忆_v1.0_Windows10+_安装程序.exe"
InstallDir "$PROGRAMFILES64\${PRODUCT_NAME}"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show

; 权限要求
RequestExecutionLevel admin

; 压缩设置
SetCompressor /SOLID lzma

; 版本信息
VIProductVersion "*******"
VIAddVersionKey "ProductName" "${PRODUCT_NAME}"
VIAddVersionKey "Comments" "Windows记忆增强程序"
VIAddVersionKey "CompanyName" "${PRODUCT_PUBLISHER}"
VIAddVersionKey "LegalCopyright" "© 2024 ${PRODUCT_PUBLISHER}"
VIAddVersionKey "FileDescription" "${PRODUCT_NAME}安装程序"
VIAddVersionKey "FileVersion" "${PRODUCT_VERSION}"
VIAddVersionKey "ProductVersion" "${PRODUCT_VERSION}"

;--------------------------------
; 界面设置
!define MUI_ABORTWARNING
!define MUI_ICON "Resources\icon.ico"
!define MUI_UNICON "Resources\icon.ico"

; 欢迎页面
!define MUI_WELCOMEPAGE_TITLE "欢迎安装 ${PRODUCT_NAME}"
!define MUI_WELCOMEPAGE_TEXT "这将在您的计算机上安装 ${PRODUCT_NAME}。$\r$\n$\r$\n${PRODUCT_NAME} 是一个专为Windows 10+优化的智能学习工具。$\r$\n$\r$\n建议您在继续安装前关闭所有其他应用程序。"

; 许可协议页面
!define MUI_LICENSEPAGE_TEXT_TOP "请仔细阅读以下许可协议。"
!define MUI_LICENSEPAGE_TEXT_BOTTOM "如果您接受协议中的条款，请点击"我同意"继续安装。"

; 组件选择页面
!define MUI_COMPONENTSPAGE_TEXT_TOP "选择您要安装的组件。"

; 安装目录页面
!define MUI_DIRECTORYPAGE_TEXT_TOP "安装程序将把 ${PRODUCT_NAME} 安装到以下文件夹。"

; 开始菜单页面
!define MUI_STARTMENUPAGE_DEFAULTFOLDER "${PRODUCT_NAME}"
!define MUI_STARTMENUPAGE_REGISTRY_ROOT "${PRODUCT_UNINST_ROOT_KEY}"
!define MUI_STARTMENUPAGE_REGISTRY_KEY "${PRODUCT_UNINST_KEY}"
!define MUI_STARTMENUPAGE_REGISTRY_VALUENAME "${PRODUCT_STARTMENU_REGVAL}"

; 安装完成页面
!define MUI_FINISHPAGE_RUN "$INSTDIR\MemoryEnhancer.exe"
!define MUI_FINISHPAGE_RUN_TEXT "运行 ${PRODUCT_NAME}"
!define MUI_FINISHPAGE_SHOWREADME "$INSTDIR\安装和使用说明.md"
!define MUI_FINISHPAGE_SHOWREADME_TEXT "查看使用说明"

;--------------------------------
; 页面定义
Var StartMenuFolder

!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_STARTMENU Application $StartMenuFolder
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

;--------------------------------
; 语言
!insertmacro MUI_LANGUAGE "SimpChinese"

;--------------------------------
; 安装类型
InstType "完整安装"
InstType "最小安装"

;--------------------------------
; 组件定义
Section "主程序" SecMain
  SectionIn RO 1 2
  
  ; 设置输出路径
  SetOutPath "$INSTDIR"
  
  ; 主程序文件
  File "publish-standalone\MemoryEnhancer.exe"
  File "publish-standalone\MemoryEnhancer.dll"
  File "publish-standalone\*.json"
  
  ; 运行时库
  File "publish-standalone\coreclr.dll"
  File "publish-standalone\clrjit.dll"
  File "publish-standalone\hostfxr.dll"
  File "publish-standalone\hostpolicy.dll"
  
  ; 系统库
  File "publish-standalone\System.*.dll"
  File "publish-standalone\Microsoft.*.dll"
  File "publish-standalone\Windows*.dll"
  File "publish-standalone\Presentation*.dll"
  
  ; 第三方库
  File "publish-standalone\EPPlus*.dll"
  File "publish-standalone\DocumentFormat.OpenXml.dll"
  File "publish-standalone\Markdig.dll"
  File "publish-standalone\WinRT.Runtime.dll"
  
  ; 其他必要文件
  File /nonfatal "publish-standalone\*.pdb"
  
  ; 创建子目录并复制文件
  SetOutPath "$INSTDIR\Resources"
  File /r "Resources\*.*"
  
  ; 语言资源
  SetOutPath "$INSTDIR"
  File /r /x "*.pdb" "publish-standalone\zh-Hans"
  
  ; 文档文件
  File "README.md"
  File "LICENSE.txt"
  File "安装和使用说明.md"
  
SectionEnd

Section "桌面快捷方式" SecDesktop
  SectionIn 1
  CreateShortCut "$DESKTOP\${PRODUCT_NAME}.lnk" "$INSTDIR\MemoryEnhancer.exe" "" "$INSTDIR\Resources\icon.ico"
SectionEnd

Section "开始菜单快捷方式" SecStartMenu
  SectionIn 1 2
  
  !insertmacro MUI_STARTMENU_WRITE_BEGIN Application
  CreateDirectory "$SMPROGRAMS\$StartMenuFolder"
  CreateShortCut "$SMPROGRAMS\$StartMenuFolder\${PRODUCT_NAME}.lnk" "$INSTDIR\MemoryEnhancer.exe" "" "$INSTDIR\Resources\icon.ico"
  CreateShortCut "$SMPROGRAMS\$StartMenuFolder\卸载${PRODUCT_NAME}.lnk" "$INSTDIR\uninst.exe"
  CreateShortCut "$SMPROGRAMS\$StartMenuFolder\使用说明.lnk" "$INSTDIR\安装和使用说明.md"
  !insertmacro MUI_STARTMENU_WRITE_END
  
SectionEnd

Section "文件关联" SecFileAssoc
  SectionIn 1
  
  ; 注册文件关联
  WriteRegStr HKCR ".memory" "" "MemoryEnhancerFile"
  WriteRegStr HKCR "MemoryEnhancerFile" "" "${PRODUCT_NAME}文件"
  WriteRegStr HKCR "MemoryEnhancerFile\DefaultIcon" "" "$INSTDIR\Resources\icon.ico"
  WriteRegStr HKCR "MemoryEnhancerFile\shell\open\command" "" '"$INSTDIR\MemoryEnhancer.exe" "%1"'
  
SectionEnd

;--------------------------------
; 组件描述
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} "安装${PRODUCT_NAME}主程序和必要的运行库。"
  !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} "在桌面创建${PRODUCT_NAME}的快捷方式。"
  !insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} "在开始菜单创建${PRODUCT_NAME}的快捷方式。"
  !insertmacro MUI_DESCRIPTION_TEXT ${SecFileAssoc} "将.memory文件与${PRODUCT_NAME}关联。"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

;--------------------------------
; 安装后处理
Section -AdditionalIcons
  WriteIniStr "$INSTDIR\${PRODUCT_NAME}.url" "InternetShortcut" "URL" "${PRODUCT_WEB_SITE}"
  !insertmacro MUI_STARTMENU_WRITE_BEGIN Application
  CreateShortCut "$SMPROGRAMS\$StartMenuFolder\访问网站.lnk" "$INSTDIR\${PRODUCT_NAME}.url"
  !insertmacro MUI_STARTMENU_WRITE_END
SectionEnd

Section -Post
  WriteUninstaller "$INSTDIR\uninst.exe"
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\MemoryEnhancer.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\Resources\icon.ico"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
SectionEnd

;--------------------------------
; 安装前检查
Function .onInit
  ; 检查系统版本
  ${IfNot} ${AtLeastWin10}
    MessageBox MB_OK|MB_ICONSTOP "此程序需要 Windows 10 或更高版本。"
    Abort
  ${EndIf}
  
  ; 检查系统架构
  ${IfNot} ${RunningX64}
    MessageBox MB_OK|MB_ICONSTOP "此程序需要 64 位系统。"
    Abort
  ${EndIf}
  
  ; 检查是否已安装
  ReadRegStr $R0 ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString"
  StrCmp $R0 "" done
  
  MessageBox MB_OKCANCEL|MB_ICONEXCLAMATION "${PRODUCT_NAME} 已经安装。$\n$\n点击"确定"卸载之前的版本，或点击"取消"取消此次安装。" IDOK uninst
  Abort
  
uninst:
  ClearErrors
  ExecWait '$R0 _?=$INSTDIR'
  
  IfErrors no_remove_uninstaller done
    no_remove_uninstaller:
  
done:
FunctionEnd

;--------------------------------
; 卸载程序
Section Uninstall
  ; 删除文件
  Delete "$INSTDIR\${PRODUCT_NAME}.url"
  Delete "$INSTDIR\uninst.exe"
  Delete "$INSTDIR\MemoryEnhancer.exe"
  Delete "$INSTDIR\*.dll"
  Delete "$INSTDIR\*.json"
  Delete "$INSTDIR\*.pdb"
  Delete "$INSTDIR\*.md"
  Delete "$INSTDIR\*.txt"
  
  ; 删除目录
  RMDir /r "$INSTDIR\Resources"
  RMDir /r "$INSTDIR\zh-Hans"
  RMDir /r "$INSTDIR\Data"
  RMDir /r "$INSTDIR\Logs"
  
  ; 删除快捷方式
  Delete "$DESKTOP\${PRODUCT_NAME}.lnk"
  
  !insertmacro MUI_STARTMENU_GETFOLDER "Application" $StartMenuFolder
  Delete "$SMPROGRAMS\$StartMenuFolder\${PRODUCT_NAME}.lnk"
  Delete "$SMPROGRAMS\$StartMenuFolder\卸载${PRODUCT_NAME}.lnk"
  Delete "$SMPROGRAMS\$StartMenuFolder\使用说明.lnk"
  Delete "$SMPROGRAMS\$StartMenuFolder\访问网站.lnk"
  RMDir "$SMPROGRAMS\$StartMenuFolder"
  
  ; 删除注册表项
  DeleteRegKey ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  DeleteRegKey HKCR ".memory"
  DeleteRegKey HKCR "MemoryEnhancerFile"
  
  ; 删除安装目录
  RMDir "$INSTDIR"
  
  SetAutoClose true
SectionEnd
