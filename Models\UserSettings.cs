using System;
using System.Windows.Media;
using System.Windows;
using MemoryEnhancer.Utils;

namespace MemoryEnhancer
{
    public class UserSettings
    {
        // 显示设置
        public int IntervalTimeInSeconds { get; set; } = Constants.DefaultIntervalTimeSeconds; // 两条内容之间的间隔时间
        public bool AutoRead { get; set; } = true;
        public bool AutoAdvance { get; set; } = true;
        public RGB TextColor { get; set; } = new RGB(0, 0, 0); // 黑色
        public RGB BackgroundColor { get; set; } = new RGB(255, 255, 255); // 白色

        // 文件组管理设置
        public string CurrentFileGroupId { get; set; } = string.Empty; // 当前选择的文件组ID
        public bool PlayTodayItemsOnly { get; set; } = true; // 是否只播放今天的项目

        // 字体设置
        public string FontFamily { get; set; } = Constants.DefaultFontFamily;
        public double FontSize { get; set; } = Constants.DefaultFontSize;
        public bool IsBold { get; set; } = false;
        public bool IsItalic { get; set; } = false;

        // 学习设置
        public int DailyNewItemsLimit { get; set; } = Constants.DefaultDailyNewItemsLimit;
        public int DailyReviewItemsLimit { get; set; } = Constants.DefaultDailyReviewItemsLimit;
        public RepetitionAlgorithm SelectedAlgorithm { get; set; } = RepetitionAlgorithm.Simple;
        public int RepeatCount { get; set; } = 1; // 单条文本重复次数

        // 上次学习日期，用于跟踪每日学习进度
        public DateTime LastStudyDate { get; set; } = DateTime.Today;
        public int TodayNewItemsStudied { get; set; } = 0;
        public int TodayReviewItemsStudied { get; set; } = 0;

        // 统计信息
        public TimeSpan TotalStudyTime { get; set; } = TimeSpan.Zero;
        public int TotalItemsStudied { get; set; } = 0;
        public int TotalReviewsCompleted { get; set; } = 0;
        public DateTime FirstStudyDate { get; set; } = DateTime.Today;
        public int ConsecutiveDaysStudied { get; set; } = 1;
        public int LongestStreak { get; set; } = 1;

        public UserSettings()
        {
            // 默认构造函数
        }

        // 将RGB转换为WPF的Color
        public Color GetTextColor()
        {
            return Color.FromRgb(TextColor.R, TextColor.G, TextColor.B);
        }

        public Color GetBackgroundColor()
        {
            return Color.FromRgb(BackgroundColor.R, BackgroundColor.G, BackgroundColor.B);
        }

        // 获取字体样式
        public FontStyle GetFontStyle()
        {
            return IsItalic ? FontStyles.Italic : FontStyles.Normal;
        }

        // 获取字体粗细
        public FontWeight GetFontWeight()
        {
            return IsBold ? FontWeights.Bold : FontWeights.Normal;
        }

        // 重置每日计数器（如果是新的一天）
        public void CheckAndResetDailyCounters()
        {
            if (LastStudyDate.Date != DateTime.Today)
            {
                // 检查连续学习天数
                if ((DateTime.Today - LastStudyDate.Date).Days == 1)
                {
                    // 连续学习天数+1
                    ConsecutiveDaysStudied++;
                    if (ConsecutiveDaysStudied > LongestStreak)
                    {
                        LongestStreak = ConsecutiveDaysStudied;
                    }
                }
                else if ((DateTime.Today - LastStudyDate.Date).Days > 1)
                {
                    // 连续学习中断
                    ConsecutiveDaysStudied = 1;
                }

                LastStudyDate = DateTime.Today;
                TodayNewItemsStudied = 0;
                TodayReviewItemsStudied = 0;
            }
        }

        // 更新学习统计信息
        public void UpdateStudyStats(TimeSpan sessionTime, bool isNewItem)
        {
            TotalStudyTime += sessionTime;
            TotalReviewsCompleted++;

            if (isNewItem)
            {
                TotalItemsStudied++;
            }
        }
    }

    // 用于JSON序列化的RGB结构
    public class RGB
    {
        public byte R { get; set; }
        public byte G { get; set; }
        public byte B { get; set; }

        public RGB()
        {
            // 默认构造函数，用于序列化
        }

        public RGB(byte r, byte g, byte b)
        {
            R = r;
            G = g;
            B = b;
        }
    }
}