using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace MemoryEnhancer.Dialogs
{
    /// <summary>
    /// ColorPickerDialog.xaml 的交互逻辑
    /// </summary>
    public partial class ColorPickerDialog : Window
    {
        public Color SelectedColor { get; set; } = Colors.Black;

        public ColorPickerDialog()
        {
            InitializeComponent();
            UpdateColorPreview();
        }

        private void ColorButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Background is SolidColorBrush brush)
            {
                SelectedColor = brush.Color;
                UpdateColorPreview();
            }
        }

        private void UpdateColorPreview()
        {
            colorPreview.Fill = new SolidColorBrush(SelectedColor);
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
