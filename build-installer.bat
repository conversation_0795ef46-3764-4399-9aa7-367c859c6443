@echo off
chcp 65001 >nul
echo ========================================
echo 我要记忆 - 安装程序构建脚本
echo ========================================
echo.

:: 检查是否存在发布文件夹
if not exist "publish-standalone" (
    echo 错误: 找不到 publish-standalone 文件夹
    echo 请先运行以下命令创建发布版本:
    echo dotnet publish MemoryEnhancer.csproj --configuration Release --output publish-standalone --runtime win-x64 --self-contained true
    pause
    exit /b 1
)

:: 创建输出目录
if not exist "installer-output" mkdir installer-output

echo 正在检查安装程序构建工具...
echo.

:: 方法1: 尝试使用 NSIS
echo [1/3] 检查 NSIS...
where makensis >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ 找到 NSIS，正在构建安装程序...
    makensis installer.nsi
    if exist "我要记忆_v1.0_Windows10+_安装程序.exe" (
        move "我要记忆_v1.0_Windows10+_安装程序.exe" installer-output\
        echo ✓ NSIS 安装程序构建成功: installer-output\我要记忆_v1.0_Windows10+_安装程序.exe
        goto :success
    ) else (
        echo ✗ NSIS 构建失败
    )
) else (
    echo ✗ 未找到 NSIS
)

echo.

:: 方法2: 尝试使用 Inno Setup
echo [2/3] 检查 Inno Setup...
where iscc >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ 找到 Inno Setup，正在构建安装程序...
    iscc installer-setup.iss
    if exist "installer-output\我要记忆_v1.0_Windows10+_安装版.exe" (
        echo ✓ Inno Setup 安装程序构建成功: installer-output\我要记忆_v1.0_Windows10+_安装版.exe
        goto :success
    ) else (
        echo ✗ Inno Setup 构建失败
    )
) else (
    echo ✗ 未找到 Inno Setup
)

echo.

:: 方法3: 尝试使用 WiX
echo [3/3] 检查 WiX Toolset...
where candle >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ 找到 WiX Toolset，正在构建 MSI 安装包...
    
    :: 编译 WiX 源文件
    candle installer.wxs -out installer-output\installer.wixobj
    if %errorlevel% == 0 (
        :: 链接生成 MSI
        light installer-output\installer.wixobj -out installer-output\我要记忆_v1.0_Windows10+.msi -ext WixUIExtension
        if %errorlevel% == 0 (
            echo ✓ WiX MSI 安装包构建成功: installer-output\我要记忆_v1.0_Windows10+.msi
            goto :success
        ) else (
            echo ✗ WiX 链接失败
        )
    ) else (
        echo ✗ WiX 编译失败
    )
) else (
    echo ✗ 未找到 WiX Toolset
)

echo.
echo ========================================
echo 构建失败: 未找到可用的安装程序构建工具
echo ========================================
echo.
echo 请安装以下工具之一:
echo.
echo 1. NSIS (推荐)
echo    下载地址: https://nsis.sourceforge.io/Download
echo    安装后将安装目录添加到 PATH 环境变量
echo.
echo 2. Inno Setup
echo    下载地址: https://jrsoftware.org/isdl.php
echo    安装后将安装目录添加到 PATH 环境变量
echo.
echo 3. WiX Toolset
echo    下载地址: https://wixtoolset.org/releases/
echo    或使用: dotnet tool install --global wix
echo.
echo 安装完成后重新运行此脚本。
echo.
pause
exit /b 1

:success
echo.
echo ========================================
echo 构建成功!
echo ========================================
echo.
echo 安装程序已生成到 installer-output 文件夹中
echo.
dir installer-output\*.exe installer-output\*.msi 2>nul
echo.
echo 您现在可以分发这个安装程序给用户使用。
echo 用户需要在 Windows 10+ 系统上以管理员权限运行安装程序。
echo.
pause
