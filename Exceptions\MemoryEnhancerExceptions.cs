using System;

namespace MemoryEnhancer.Exceptions
{
    /// <summary>
    /// 我要记忆应用程序的基础异常类
    /// </summary>
    public class MemoryEnhancerException : Exception
    {
        public MemoryEnhancerException() : base() { }

        public MemoryEnhancerException(string message) : base(message) { }

        public MemoryEnhancerException(string message, Exception innerException)
            : base(message, innerException) { }
    }

    /// <summary>
    /// 数据访问相关异常
    /// </summary>
    public class DataAccessException : MemoryEnhancerException
    {
        public DataAccessException() : base() { }

        public DataAccessException(string message) : base(message) { }

        public DataAccessException(string message, Exception innerException)
            : base(message, innerException) { }
    }

    /// <summary>
    /// 设置相关异常
    /// </summary>
    public class SettingsException : MemoryEnhancerException
    {
        public SettingsException() : base() { }

        public SettingsException(string message) : base(message) { }

        public SettingsException(string message, Exception innerException)
            : base(message, innerException) { }
    }

    /// <summary>
    /// 文件导入相关异常
    /// </summary>
    public class ImportException : MemoryEnhancerException
    {
        public string? FilePath { get; }

        public ImportException() : base() { }

        public ImportException(string message) : base(message) { }

        public ImportException(string message, string filePath) : base(message)
        {
            FilePath = filePath;
        }

        public ImportException(string message, Exception innerException)
            : base(message, innerException) { }

        public ImportException(string message, string filePath, Exception innerException)
            : base(message, innerException)
        {
            FilePath = filePath;
        }
    }

    /// <summary>
    /// 语音合成相关异常
    /// </summary>
    public class SpeechException : MemoryEnhancerException
    {
        public SpeechException() : base() { }

        public SpeechException(string message) : base(message) { }

        public SpeechException(string message, Exception innerException)
            : base(message, innerException) { }
    }
}
