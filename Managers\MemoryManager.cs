using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Windows;
using MemoryEnhancer.Exceptions;
using MemoryEnhancer.Utils;

namespace MemoryEnhancer
{
    public static class MemoryManager
    {
        private static readonly string AppDataFolder = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            Constants.AppDataFolderName);

        private static readonly string MemoryItemsFilePath = Path.Combine(AppDataFolder, Constants.MemoryItemsFileName);
        private static readonly string FileGroupsFilePath = Path.Combine(AppDataFolder, Constants.FileGroupsFileName);
        private static readonly string ProgressFilePath = Path.Combine(AppDataFolder, Constants.ProgressFileName);
        private static readonly string StatisticsFilePath = Path.Combine(AppDataFolder, Constants.StatisticsFileName);

        // 确保应用数据文件夹存在
        static MemoryManager()
        {
            if (!Directory.Exists(AppDataFolder))
            {
                Directory.CreateDirectory(AppDataFolder);
            }
        }

        public static List<MemoryItem> LoadMemoryItems()
        {
            try
            {
                if (!File.Exists(MemoryItemsFilePath))
                {
                    Logger.Info("记忆项目文件不存在，返回空列表");
                    return new List<MemoryItem>();
                }

                var json = File.ReadAllText(MemoryItemsFilePath);
                if (string.IsNullOrWhiteSpace(json))
                {
                    Logger.Warning("记忆项目文件为空");
                    return new List<MemoryItem>();
                }

                var items = JsonSerializer.Deserialize<List<MemoryItem>>(json, Constants.GetJsonSerializerOptions()) ?? new List<MemoryItem>();
                Logger.Info($"成功加载 {items.Count} 个记忆项目");
                return items;
            }
            catch (UnauthorizedAccessException ex)
            {
                var message = "无法访问记忆项目文件：权限不足";
                Logger.Error(message, ex);
                throw new DataAccessException(message, ex);
            }
            catch (JsonException ex)
            {
                var message = "记忆项目文件格式错误";
                Logger.Error(message, ex);
                throw new DataAccessException(message, ex);
            }
            catch (IOException ex)
            {
                var message = "读取记忆项目文件时发生IO错误";
                Logger.Error(message, ex);
                throw new DataAccessException(message, ex);
            }
            catch (Exception ex)
            {
                var message = "加载记忆项目时发生未知错误";
                Logger.Error(message, ex);
                throw new DataAccessException(message, ex);
            }
        }

        public static void SaveMemoryItems(List<MemoryItem> items)
        {
            if (items == null)
            {
                Logger.Warning("尝试保存空的记忆项目列表");
                return;
            }

            try
            {
                var json = JsonSerializer.Serialize(items, Constants.GetJsonSerializerOptions());

                // 先写入临时文件，然后替换原文件，确保原子性操作
                var tempFilePath = MemoryItemsFilePath + Constants.TempFileExtension;
                File.WriteAllText(tempFilePath, json);

                if (File.Exists(MemoryItemsFilePath))
                {
                    File.Replace(tempFilePath, MemoryItemsFilePath, null);
                }
                else
                {
                    File.Move(tempFilePath, MemoryItemsFilePath);
                }

                Logger.Info($"成功保存 {items.Count} 个记忆项目");
            }
            catch (UnauthorizedAccessException ex)
            {
                var message = "无法保存记忆项目文件：权限不足";
                Logger.Error(message, ex);
                throw new DataAccessException(message, ex);
            }
            catch (IOException ex)
            {
                var message = "保存记忆项目文件时发生IO错误";
                Logger.Error(message, ex);
                throw new DataAccessException(message, ex);
            }
            catch (Exception ex)
            {
                var message = "保存记忆项目时发生未知错误";
                Logger.Error(message, ex);
                throw new DataAccessException(message, ex);
            }
        }

        public static int LoadProgress()
        {
            try
            {
                if (File.Exists(ProgressFilePath))
                {
                    var json = File.ReadAllText(ProgressFilePath);
                    return JsonSerializer.Deserialize<int>(json, Constants.GetJsonSerializerOptions());
                }
            }
            catch (Exception ex)
            {
                Logger.Error("加载进度时出错", ex);
                MessageBox.Show($"加载进度时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            return 0;
        }

        public static void SaveProgress(int currentIndex)
        {
            try
            {
                var json = JsonSerializer.Serialize(currentIndex, Constants.GetJsonSerializerOptions());
                File.WriteAllText(ProgressFilePath, json);
            }
            catch (Exception ex)
            {
                Logger.Error("保存进度时出错", ex);
                MessageBox.Show($"保存进度时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 保存文件组的播放进度
        public static void SaveFileGroupProgress(string groupId, int currentIndex)
        {
            try
            {
                var fileGroups = LoadFileGroups();
                var group = fileGroups.FirstOrDefault(g => g.Id == groupId);
                if (group != null)
                {
                    group.LastPlayedIndex = currentIndex;
                    SaveFileGroups(fileGroups);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存文件组进度时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 获取文件组的播放进度
        public static int LoadFileGroupProgress(string groupId)
        {
            try
            {
                var group = GetFileGroupById(groupId);
                return group?.LastPlayedIndex ?? 0;
            }
            catch (Exception)
            {
                return 0;
            }
        }

        // 保存统计数据
        public static void SaveStatistics(UserSettings settings)
        {
            try
            {
                // 创建一个只包含统计数据的对象
                var statsData = new
                {
                    settings.TotalStudyTime,
                    settings.TotalItemsStudied,
                    settings.TotalReviewsCompleted,
                    settings.FirstStudyDate,
                    settings.ConsecutiveDaysStudied,
                    settings.LongestStreak
                };

                var json = JsonSerializer.Serialize(statsData, Constants.GetJsonSerializerOptions());
                File.WriteAllText(StatisticsFilePath, json);
            }
            catch (Exception ex)
            {
                Logger.Error("保存统计数据时出错", ex);
                MessageBox.Show($"保存统计数据时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 加载统计数据
        public static void LoadStatistics(UserSettings settings)
        {
            try
            {
                if (File.Exists(StatisticsFilePath))
                {
                    var json = File.ReadAllText(StatisticsFilePath);

                    // 检查文件是否为空或只包含空白字符
                    if (string.IsNullOrWhiteSpace(json))
                    {
                        return; // 静默返回，使用默认值
                    }

                    var statsData = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(json);

                    if (statsData != null)
                    {
                        // 安全地读取每个统计值，如果读取失败则保持默认值
                        try
                        {
                            if (statsData.TryGetValue("TotalStudyTime", out var totalStudyTimeElement))
                            {
                                settings.TotalStudyTime = TimeSpan.FromTicks(totalStudyTimeElement.GetInt64());
                            }
                        }
                        catch { /* 忽略单个字段的错误 */ }

                        try
                        {
                            if (statsData.TryGetValue("TotalItemsStudied", out var totalItemsStudiedElement))
                            {
                                settings.TotalItemsStudied = totalItemsStudiedElement.GetInt32();
                            }
                        }
                        catch { /* 忽略单个字段的错误 */ }

                        try
                        {
                            if (statsData.TryGetValue("TotalReviewsCompleted", out var totalReviewsCompletedElement))
                            {
                                settings.TotalReviewsCompleted = totalReviewsCompletedElement.GetInt32();
                            }
                        }
                        catch { /* 忽略单个字段的错误 */ }

                        try
                        {
                            if (statsData.TryGetValue("FirstStudyDate", out var firstStudyDateElement))
                            {
                                settings.FirstStudyDate = firstStudyDateElement.GetDateTime();
                            }
                        }
                        catch { /* 忽略单个字段的错误 */ }

                        try
                        {
                            if (statsData.TryGetValue("ConsecutiveDaysStudied", out var consecutiveDaysStudiedElement))
                            {
                                settings.ConsecutiveDaysStudied = consecutiveDaysStudiedElement.GetInt32();
                            }
                        }
                        catch { /* 忽略单个字段的错误 */ }

                        try
                        {
                            if (statsData.TryGetValue("LongestStreak", out var longestStreakElement))
                            {
                                settings.LongestStreak = longestStreakElement.GetInt32();
                            }
                        }
                        catch { /* 忽略单个字段的错误 */ }
                    }
                }
                // 如果文件不存在，静默返回，使用默认值
            }
            catch (Exception)
            {
                // 静默处理统计数据加载错误，不显示错误消息给用户
                // 程序将使用默认的统计值继续运行
            }
        }

        // 文件分组管理方法
        public static List<MemoryFileGroup> LoadFileGroups()
        {
            try
            {
                if (File.Exists(FileGroupsFilePath))
                {
                    var json = File.ReadAllText(FileGroupsFilePath);
                    if (string.IsNullOrWhiteSpace(json))
                    {
                        Logger.Info("文件分组文件为空，返回空列表");
                        return new List<MemoryFileGroup>();
                    }
                    return JsonSerializer.Deserialize<List<MemoryFileGroup>>(json, Constants.GetJsonSerializerOptions()) ?? new List<MemoryFileGroup>();
                }
                else
                {
                    Logger.Info("文件分组文件不存在，返回空列表");
                    return new List<MemoryFileGroup>();
                }
            }
            catch (JsonException ex)
            {
                Logger.Error("文件分组JSON格式错误", ex);
                // 不显示错误消息给用户，静默处理
                return new List<MemoryFileGroup>();
            }
            catch (Exception ex)
            {
                Logger.Error("加载文件分组时出错", ex);
                // 不显示错误消息给用户，静默处理
                return new List<MemoryFileGroup>();
            }
        }

        public static void SaveFileGroups(List<MemoryFileGroup> fileGroups)
        {
            try
            {
                var json = JsonSerializer.Serialize(fileGroups, Constants.GetJsonSerializerOptions());
                File.WriteAllText(FileGroupsFilePath, json);
            }
            catch (Exception ex)
            {
                Logger.Error("保存文件分组时出错", ex);
                MessageBox.Show($"保存文件分组时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public static MemoryFileGroup? GetFileGroupById(string groupId)
        {
            var fileGroups = LoadFileGroups();
            return fileGroups.FirstOrDefault(g => g.Id == groupId);
        }

        public static bool DeleteFileGroup(string groupId)
        {
            try
            {
                var fileGroups = LoadFileGroups();
                var groupToRemove = fileGroups.FirstOrDefault(g => g.Id == groupId);
                if (groupToRemove != null)
                {
                    fileGroups.Remove(groupToRemove);
                    SaveFileGroups(fileGroups);
                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除文件分组时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            return false;
        }

        public static bool RenameFileGroup(string groupId, string newName)
        {
            try
            {
                var fileGroups = LoadFileGroups();
                var group = fileGroups.FirstOrDefault(g => g.Id == groupId);
                if (group != null)
                {
                    group.Name = newName;
                    SaveFileGroups(fileGroups);
                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重命名文件分组时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            return false;
        }

        // 新的文件分组导入方法
        public static string? ImportFileAsGroup(string filePath, string? groupName = null)
        {
            try
            {
                if (!FileImportHelper.IsSupportedFile(filePath))
                {
                    MessageBox.Show("不支持的文件格式", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return null;
                }

                var items = new List<MemoryItem>();
                bool success = FileImportHelper.ImportFile(filePath, items);

                if (success && items.Count > 0)
                {
                    // 创建文件分组
                    var fileName = Path.GetFileNameWithoutExtension(filePath);
                    var finalGroupName = groupName ?? fileName;

                    var fileGroup = new MemoryFileGroup(finalGroupName, Path.GetFileName(filePath), items);

                    // 保存到文件分组
                    var fileGroups = LoadFileGroups();
                    fileGroups.Add(fileGroup);
                    SaveFileGroups(fileGroups);

                    Logger.Info($"成功导入文件组 '{finalGroupName}'，包含 {items.Count} 个项目");
                    return fileGroup.Id;
                }
                return null;
            }
            catch (ImportException ex)
            {
                Logger.Error($"导入文件时出错: {ex.Message}", ex);
                MessageBox.Show($"从文件导入时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }
            catch (Exception ex)
            {
                Logger.Error($"导入文件时发生未知错误: {ex.Message}", ex);
                MessageBox.Show($"从文件导入时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }
        }

        // 保留原有方法用于兼容性（将导入到旧的统一列表中）
        public static bool ImportFromTextFile(string filePath)
        {
            try
            {
                if (!FileImportHelper.IsSupportedFile(filePath))
                {
                    MessageBox.Show("不支持的文件格式", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                var items = LoadMemoryItems();
                bool success = FileImportHelper.ImportFile(filePath, items);

                if (success)
                {
                    SaveMemoryItems(items);
                    Logger.Info($"成功导入文件到统一列表: {Path.GetFileName(filePath)}");
                    return true;
                }
                return false;
            }
            catch (ImportException ex)
            {
                Logger.Error($"导入文件时出错: {ex.Message}", ex);
                MessageBox.Show($"从文件导入时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error($"导入文件时发生未知错误: {ex.Message}", ex);
                MessageBox.Show($"从文件导入时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        // 旧的导入方法已移至 FileImportHelper 类中

        // 所有导入方法已移至 FileImportHelper 类中，以减少代码重复

        // 获取指定文件组的项目（支持今天项目或全部项目）
        public static List<MemoryItem> GetFileGroupItems(UserSettings settings)
        {
            // 如果没有选择文件组，尝试从旧的统一列表加载
            if (string.IsNullOrEmpty(settings.CurrentFileGroupId))
            {
                return GetLegacyItems(settings);
            }

            var fileGroup = GetFileGroupById(settings.CurrentFileGroupId);
            if (fileGroup == null)
            {
                // 如果选择的文件组不存在，回退到旧的统一列表
                return GetLegacyItems(settings);
            }

            // 确保每日计数器是最新的
            settings.CheckAndResetDailyCounters();

            if (settings.PlayTodayItemsOnly)
            {
                return fileGroup.GetTodayItems(settings);
            }
            else
            {
                return fileGroup.GetAllItems();
            }
        }

        // 获取旧的统一列表项目（用于兼容性）
        private static List<MemoryItem> GetLegacyItems(UserSettings settings)
        {
            var allItems = LoadMemoryItems();

            // 确保每日计数器是最新的
            settings.CheckAndResetDailyCounters();

            // 如果没有任何项目，返回空列表
            if (allItems.Count == 0)
            {
                return new List<MemoryItem>();
            }

            if (settings.PlayTodayItemsOnly)
            {
                return GetTodayItemsFromList(allItems, settings);
            }
            else
            {
                // 为每个项目启动学习会话
                foreach (var item in allItems)
                {
                    item.StartStudySession();
                }
                return allItems;
            }
        }

        private static List<MemoryItem> GetTodayItemsFromList(List<MemoryItem> allItems, UserSettings settings)
        {
            var today = DateTime.Today;

            // 优先获取新项目（ReviewCount == 0且NextReviewDate <= 今天）
            var newItems = allItems
                .Where(i => i.ReviewCount == 0 && i.NextReviewDate.Date <= today)
                .Take(Math.Max(0, settings.DailyNewItemsLimit - settings.TodayNewItemsStudied))
                .ToList();

            // 获取今天应该复习的项目（排除新项目）
            var reviewItems = allItems
                .Where(i => i.ReviewCount > 0 && i.NextReviewDate.Date <= today)
                .OrderBy(i => i.NextReviewDate)
                .Take(Math.Max(0, settings.DailyReviewItemsLimit - settings.TodayReviewItemsStudied))
                .ToList();

            // 合并新项目和复习项目
            var todayItems = new List<MemoryItem>();
            todayItems.AddRange(newItems);
            todayItems.AddRange(reviewItems);

            // 如果今天的项目数量不足，且还有未学习的新项目，添加更多新项目
            if (todayItems.Count < 10 && allItems.Any(i => i.ReviewCount == 0)) // 至少保证有一些内容可以学习
            {
                var additionalNewItems = allItems
                    .Where(i => i.ReviewCount == 0 && !todayItems.Contains(i))
                    .Take(10 - todayItems.Count)
                    .ToList();

                todayItems.AddRange(additionalNewItems);
            }

            // 为每个项目启动学习会话
            foreach (var item in todayItems)
            {
                item.StartStudySession();
            }

            return todayItems;
        }

        // 保留原有方法用于兼容性
        public static List<MemoryItem> GetAllAvailableItems()
        {
            var allItems = LoadMemoryItems();

            // 如果没有任何项目，返回空列表
            if (allItems.Count == 0)
            {
                return new List<MemoryItem>();
            }

            // 为每个项目启动学习会话
            foreach (var item in allItems)
            {
                item.StartStudySession();
            }

            return allItems;
        }

        public static List<MemoryItem> GetTodayItems(UserSettings settings)
        {
            var allItems = LoadMemoryItems();

            // 确保每日计数器是最新的
            settings.CheckAndResetDailyCounters();

            // 如果没有任何项目，返回空列表
            if (allItems.Count == 0)
            {
                return new List<MemoryItem>();
            }

            return GetTodayItemsFromList(allItems, settings);
        }

        // 获取统计摘要
        public static Dictionary<string, string> GetStatisticsSummary(UserSettings settings)
        {
            var stats = new Dictionary<string, string>();

            // 计算总学习天数
            int totalDays = (DateTime.Today - settings.FirstStudyDate).Days + 1;

            stats.Add("总学习天数", totalDays.ToString());
            stats.Add("连续学习天数", settings.ConsecutiveDaysStudied.ToString());
            stats.Add("最长连续学习", settings.LongestStreak.ToString());
            stats.Add("总学习项目数", settings.TotalItemsStudied.ToString());
            stats.Add("总复习次数", settings.TotalReviewsCompleted.ToString());

            // 格式化总学习时间
            TimeSpan totalTime = settings.TotalStudyTime;
            string formattedTime = $"{(int)totalTime.TotalHours}小时{totalTime.Minutes}分钟";
            stats.Add("总学习时间", formattedTime);

            // 计算平均每天学习时间
            if (totalDays > 0)
            {
                double avgMinutesPerDay = totalTime.TotalMinutes / totalDays;
                stats.Add("平均每天学习", $"{avgMinutesPerDay:F1}分钟");
            }
            else
            {
                stats.Add("平均每天学习", "0分钟");
            }

            return stats;
        }
    }
}