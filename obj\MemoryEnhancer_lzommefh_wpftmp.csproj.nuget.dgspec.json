{"format": 1, "restore": {"K:\\xiecx\\VC\\我要记忆 1.0 0526\\MemoryEnhancer.csproj": {}}, "projects": {"K:\\xiecx\\VC\\我要记忆 1.0 0526\\MemoryEnhancer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "K:\\xiecx\\VC\\我要记忆 1.0 0526\\MemoryEnhancer.csproj", "projectName": "MemoryEnhancer", "projectPath": "K:\\xiecx\\VC\\我要记忆 1.0 0526\\MemoryEnhancer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "K:\\xiecx\\VC\\我要记忆 1.0 0526\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows10.0.17763.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows10.0.17763": {"targetAlias": "net6.0-windows10.0.17763.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows10.0.17763": {"targetAlias": "net6.0-windows10.0.17763.0", "dependencies": {"DocumentFormat.OpenXml": {"target": "Package", "version": "[2.20.0, )"}, "EPPlus": {"target": "Package", "version": "[6.2.10, )"}, "Markdig": {"target": "Package", "version": "[0.33.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[7.0.0, )"}, "System.Speech": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Crossgen2.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.17763.52, 10.0.17763.52]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}