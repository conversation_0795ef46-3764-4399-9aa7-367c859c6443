@echo off
chcp 65001 >nul
echo ========================================
echo 我要记忆 - 自动打包脚本
echo ========================================
echo.

:: 检查是否存在发布文件夹
if not exist "publish-standalone" (
    echo 正在创建发布版本...
    dotnet publish MemoryEnhancer.csproj --configuration Release --output publish-standalone --runtime win-x64 --self-contained true --verbosity minimal
    if errorlevel 1 (
        echo 错误: 发布失败
        pause
        exit /b 1
    )
    echo ✓ 发布版本创建成功
    echo.
)

:: 创建输出目录
if not exist "installer-output" mkdir installer-output

echo 正在创建安装程序...
echo.

:: 复制安装脚本到输出目录
copy "Simple-Installer.ps1" "installer-output\Install-MemoryEnhancer.ps1" >nul

:: 创建便携版压缩包
echo 正在创建便携版...
powershell -Command "Compress-Archive -Path 'publish-standalone\*' -DestinationPath 'installer-output\我要记忆_v1.0_便携版.zip' -Force"

:: 创建安装版压缩包（包含安装脚本和程序文件）
echo 正在创建安装版...
powershell -Command "Compress-Archive -Path 'Simple-Installer.ps1','publish-standalone' -DestinationPath 'installer-output\我要记忆_v1.0_安装版.zip' -Force"

:: 创建使用说明
echo 正在创建使用说明...
(
echo # 我要记忆 v1.0 - 安装包说明
echo.
echo ## 📦 安装包类型
echo.
echo ### 1. 便携版
echo - 文件名: 我要记忆_v1.0_便携版.zip
echo - 使用方法: 解压后直接运行 MemoryEnhancer.exe
echo - 优点: 无需安装，解压即用
echo - 缺点: 不会集成到系统中
echo.
echo ### 2. 安装版 ^(推荐^)
echo - 文件名: 我要记忆_v1.0_安装版.zip
echo - 使用方法: 
echo   1. 解压 我要记忆_v1.0_安装版.zip
echo   2. 右键点击 Simple-Installer.ps1
echo   3. 选择 "使用PowerShell运行"
echo   4. 按照提示完成安装
echo - 优点: 完整的系统集成，创建快捷方式，支持卸载
echo.
echo ## 🎯 系统要求
echo - Windows 10 版本 1809 ^(Build 17763^) 或更高
echo - 64位系统
echo - 管理员权限 ^(仅安装版需要^)
echo.
echo ## ✅ 安装版特性
echo - ✓ 需要管理员权限安装
echo - ✓ 安装到 Program Files 目录
echo - ✓ 创建桌面快捷方式
echo - ✓ 创建开始菜单快捷方式
echo - ✓ 注册到系统程序列表
echo - ✓ 支持通过控制面板卸载
echo - ✓ 自包含 .NET 运行时
echo.
echo ## 🚀 使用建议
echo 推荐使用安装版，因为它提供完整的Windows程序体验。
echo 如果您需要便携使用，可以选择便携版。
) > "installer-output\安装说明.txt"

echo.
echo ========================================
echo 打包完成！
echo ========================================
echo.
echo 生成的文件:
dir "installer-output" /b
echo.
echo 文件位置: installer-output 文件夹
echo.
echo 安装版使用方法:
echo 1. 解压 我要记忆_v1.0_安装版.zip
echo 2. 右键点击 Simple-Installer.ps1
echo 3. 选择 "使用PowerShell运行"
echo 4. 按照提示完成安装
echo.
echo 便携版使用方法:
echo 1. 解压 我要记忆_v1.0_便携版.zip
echo 2. 直接运行 MemoryEnhancer.exe
echo.
pause
