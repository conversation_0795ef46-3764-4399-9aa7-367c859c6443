using System;
using System.IO;
using System.Text.Json;
using System.Windows;
using MemoryEnhancer.Exceptions;
using MemoryEnhancer.Utils;

namespace MemoryEnhancer
{
    public static class SettingsManager
    {
        private static readonly string AppDataFolder = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "MemoryEnhancer");

        private static readonly string SettingsFileName = "settings.json";

        // 确保应用数据文件夹存在
        static SettingsManager()
        {
            if (!Directory.Exists(AppDataFolder))
            {
                Directory.CreateDirectory(AppDataFolder);
            }
        }

        public static UserSettings LoadSettings()
        {
            try
            {
                var filePath = Path.Combine(AppDataFolder, SettingsFileName);

                if (!File.Exists(filePath))
                {
                    Logger.Info("设置文件不存在，使用默认设置");
                    return new UserSettings();
                }

                var json = File.ReadAllText(filePath);
                if (string.IsNullOrWhiteSpace(json))
                {
                    Logger.Warning("设置文件为空，使用默认设置");
                    return new UserSettings();
                }

                var settings = JsonSerializer.Deserialize<UserSettings>(json) ?? new UserSettings();
                Logger.Info("成功加载用户设置");
                return settings;
            }
            catch (UnauthorizedAccessException ex)
            {
                var message = "无法访问设置文件：权限不足";
                Logger.Error(message, ex);
                throw new SettingsException(message, ex);
            }
            catch (JsonException ex)
            {
                var message = "设置文件格式错误，使用默认设置";
                Logger.Warning(message);
                Logger.Error("设置文件JSON格式错误", ex);
                return new UserSettings(); // 返回默认设置而不是抛出异常
            }
            catch (IOException ex)
            {
                var message = "读取设置文件时发生IO错误";
                Logger.Error(message, ex);
                throw new SettingsException(message, ex);
            }
            catch (Exception ex)
            {
                var message = "加载设置时发生未知错误";
                Logger.Error(message, ex);
                throw new SettingsException(message, ex);
            }
        }

        public static void SaveSettings(UserSettings settings)
        {
            if (settings == null)
            {
                Logger.Warning("尝试保存空的设置对象");
                return;
            }

            try
            {
                var filePath = Path.Combine(AppDataFolder, SettingsFileName);

                var options = new JsonSerializerOptions { WriteIndented = true };
                var json = JsonSerializer.Serialize(settings, options);

                // 先写入临时文件，然后替换原文件，确保原子性操作
                var tempFilePath = filePath + ".tmp";
                File.WriteAllText(tempFilePath, json);

                if (File.Exists(filePath))
                {
                    File.Replace(tempFilePath, filePath, null);
                }
                else
                {
                    File.Move(tempFilePath, filePath);
                }

                Logger.Info("成功保存用户设置");
            }
            catch (UnauthorizedAccessException ex)
            {
                var message = "无法保存设置文件：权限不足";
                Logger.Error(message, ex);
                throw new SettingsException(message, ex);
            }
            catch (IOException ex)
            {
                var message = "保存设置文件时发生IO错误";
                Logger.Error(message, ex);
                throw new SettingsException(message, ex);
            }
            catch (Exception ex)
            {
                var message = "保存设置时发生未知错误";
                Logger.Error(message, ex);
                throw new SettingsException(message, ex);
            }
        }
    }
}
