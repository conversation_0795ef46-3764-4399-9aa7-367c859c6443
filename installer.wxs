<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  
  <!-- 产品定义 -->
  <Product Id="*" 
           Name="我要记忆" 
           Language="2052" 
           Version="*******" 
           Manufacturer="周少文" 
           UpgradeCode="B8F9C8E5-4A2D-4F1E-9B3C-7D6E8F9A0B1C">
    
    <!-- 安装包信息 -->
    <Package InstallerVersion="200" 
             Compressed="yes" 
             InstallScope="perMachine"
             Platform="x64"
             Description="我要记忆 - Windows记忆增强程序"
             Comments="专为Windows 10+优化的智能学习工具"
             Manufacturer="周少文" />

    <!-- 系统要求 -->
    <Condition Message="此程序需要 Windows 10 版本 1809 或更高版本。">
      <![CDATA[Installed OR (VersionNT >= 1000 AND VersionNT64)]]>
    </Condition>

    <!-- 升级规则 -->
    <MajorUpgrade DowngradeErrorMessage="已安装更新版本的我要记忆。" />
    
    <!-- 媒体定义 -->
    <MediaTemplate EmbedCab="yes" />

    <!-- 功能定义 -->
    <Feature Id="ProductFeature" Title="我要记忆主程序" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
      <ComponentGroupRef Id="ResourceComponents" />
      <ComponentGroupRef Id="DocumentComponents" />
    </Feature>

    <!-- 可选功能 -->
    <Feature Id="DesktopShortcut" Title="桌面快捷方式" Level="1000">
      <ComponentRef Id="DesktopShortcutComponent" />
    </Feature>

    <Feature Id="StartMenuShortcut" Title="开始菜单快捷方式" Level="1">
      <ComponentRef Id="StartMenuShortcutComponent" />
    </Feature>

    <!-- 用户界面 -->
    <UIRef Id="WixUI_FeatureTree" />
    <UIRef Id="WixUI_ErrorProgressText" />
    
    <!-- 许可协议 -->
    <WixVariable Id="WixUILicenseRtf" Value="LICENSE.rtf" />
    
    <!-- 安装图标 -->
    <Icon Id="icon.ico" SourceFile="Resources\icon.ico" />
    <Property Id="ARPPRODUCTICON" Value="icon.ico" />
    
    <!-- 程序信息 -->
    <Property Id="ARPHELPLINK" Value="https://github.com/memory-enhancer/support" />
    <Property Id="ARPURLINFOABOUT" Value="https://github.com/memory-enhancer" />
    <Property Id="ARPNOREPAIR" Value="1" />
    <Property Id="ARPNOMODIFY" Value="1" />

  </Product>

  <!-- 目录结构 -->
  <Fragment>
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFiles64Folder">
        <Directory Id="INSTALLFOLDER" Name="我要记忆" />
      </Directory>
      
      <!-- 开始菜单 -->
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="我要记忆" />
      </Directory>
      
      <!-- 桌面 -->
      <Directory Id="DesktopFolder" Name="Desktop" />
    </Directory>
  </Fragment>

  <!-- 主程序组件 -->
  <Fragment>
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      
      <!-- 主程序 -->
      <Component Id="MainExecutable" Guid="*">
        <File Id="MemoryEnhancerExe" 
              Source="publish-standalone\MemoryEnhancer.exe" 
              KeyPath="yes" 
              Checksum="yes">
          <Shortcut Id="StartMenuShortcut"
                    Directory="ApplicationProgramsFolder"
                    Name="我要记忆"
                    Description="Windows记忆增强程序"
                    Icon="icon.ico"
                    WorkingDirectory="INSTALLFOLDER" />
        </File>
        
        <!-- 注册表项 -->
        <RegistryValue Root="HKCU" 
                       Key="Software\我要记忆" 
                       Name="InstallPath" 
                       Type="string" 
                       Value="[INSTALLFOLDER]" 
                       KeyPath="no" />
      </Component>

      <!-- 运行时库 -->
      <Component Id="RuntimeLibraries" Guid="*">
        <File Source="publish-standalone\coreclr.dll" KeyPath="yes" />
        <File Source="publish-standalone\clrjit.dll" />
        <File Source="publish-standalone\hostfxr.dll" />
        <File Source="publish-standalone\hostpolicy.dll" />
        <File Source="publish-standalone\System.Private.CoreLib.dll" />
        <File Source="publish-standalone\System.Runtime.dll" />
        <File Source="publish-standalone\System.Speech.dll" />
      </Component>

      <!-- 应用程序库 -->
      <Component Id="ApplicationLibraries" Guid="*">
        <File Source="publish-standalone\MemoryEnhancer.dll" KeyPath="yes" />
        <File Source="publish-standalone\MemoryEnhancer.deps.json" />
        <File Source="publish-standalone\MemoryEnhancer.runtimeconfig.json" />
        <File Source="publish-standalone\EPPlus.dll" />
        <File Source="publish-standalone\DocumentFormat.OpenXml.dll" />
        <File Source="publish-standalone\Markdig.dll" />
      </Component>

      <!-- WPF相关库 -->
      <Component Id="WPFLibraries" Guid="*">
        <File Source="publish-standalone\PresentationCore.dll" KeyPath="yes" />
        <File Source="publish-standalone\PresentationFramework.dll" />
        <File Source="publish-standalone\WindowsBase.dll" />
        <File Source="publish-standalone\System.Xaml.dll" />
        <File Source="publish-standalone\Microsoft.Windows.SDK.NET.dll" />
      </Component>

    </ComponentGroup>
  </Fragment>

  <!-- 资源组件 -->
  <Fragment>
    <ComponentGroup Id="ResourceComponents" Directory="INSTALLFOLDER">
      <Component Id="ResourceFiles" Guid="*">
        <CreateFolder />
        <File Source="Resources\icon.ico" KeyPath="yes" />
        <File Source="Resources\icon.svg" />
        <File Source="Resources\icon_16x16.png" />
        <File Source="Resources\icon_32x32.png" />
        <File Source="Resources\icon_48x48.png" />
        <File Source="Resources\icon_64x64.png" />
        <File Source="Resources\icon_128x128.png" />
        <File Source="Resources\icon_256x256.png" />
      </Component>
    </ComponentGroup>
  </Fragment>

  <!-- 文档组件 -->
  <Fragment>
    <ComponentGroup Id="DocumentComponents" Directory="INSTALLFOLDER">
      <Component Id="DocumentFiles" Guid="*">
        <File Source="README.md" KeyPath="yes" />
        <File Source="LICENSE.txt" />
        <File Source="安装和使用说明.md" />
      </Component>
    </ComponentGroup>
  </Fragment>

  <!-- 桌面快捷方式组件 -->
  <Fragment>
    <Component Id="DesktopShortcutComponent" Directory="DesktopFolder" Guid="*">
      <Shortcut Id="DesktopShortcut"
                Name="我要记忆"
                Description="Windows记忆增强程序"
                Target="[INSTALLFOLDER]MemoryEnhancer.exe"
                Icon="icon.ico"
                WorkingDirectory="INSTALLFOLDER" />
      <RemoveFolder Id="DesktopFolder" On="uninstall" />
      <RegistryValue Root="HKCU" 
                     Key="Software\我要记忆\Shortcuts" 
                     Name="Desktop" 
                     Type="integer" 
                     Value="1" 
                     KeyPath="yes" />
    </Component>
  </Fragment>

  <!-- 开始菜单快捷方式组件 -->
  <Fragment>
    <Component Id="StartMenuShortcutComponent" Directory="ApplicationProgramsFolder" Guid="*">
      <Shortcut Id="UninstallShortcut"
                Name="卸载我要记忆"
                Description="卸载我要记忆程序"
                Target="[SystemFolder]msiexec.exe"
                Arguments="/x [ProductCode]" />
      <RemoveFolder Id="ApplicationProgramsFolder" On="uninstall" />
      <RegistryValue Root="HKCU" 
                     Key="Software\我要记忆\Shortcuts" 
                     Name="StartMenu" 
                     Type="integer" 
                     Value="1" 
                     KeyPath="yes" />
    </Component>
  </Fragment>

</Wix>
