﻿#pragma checksum "..\..\..\..\Dialogs\ColorPickerDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CF73230823F56D47A0075C671805ACBEE5954BB4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MemoryEnhancer.Dialogs {
    
    
    /// <summary>
    /// ColorPickerDialog
    /// </summary>
    public partial class ColorPickerDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 19 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color1;
        
        #line default
        #line hidden
        
        
        #line 20 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color2;
        
        #line default
        #line hidden
        
        
        #line 21 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color3;
        
        #line default
        #line hidden
        
        
        #line 22 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color4;
        
        #line default
        #line hidden
        
        
        #line 23 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color5;
        
        #line default
        #line hidden
        
        
        #line 24 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color6;
        
        #line default
        #line hidden
        
        
        #line 25 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color7;
        
        #line default
        #line hidden
        
        
        #line 26 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color8;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color9;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color10;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color11;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color12;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color13;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color14;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color15;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color16;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color17;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color18;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color19;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color20;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color21;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color22;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color23;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color24;
        
        #line default
        #line hidden
        
        
        #line 46 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color25;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color26;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color27;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color28;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color29;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color30;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color31;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color32;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color33;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color34;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color35;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color36;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color37;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color38;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color39;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color40;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color41;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color42;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color43;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color44;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color45;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color46;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color47;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button Color48;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle colorPreview;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button okButton;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button cancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MemoryEnhancer;V1.0.0.0;component/dialogs/colorpickerdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.Color1 = ((System.Windows.Controls.Button)(target));
            
            #line 19 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color1.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.Color2 = ((System.Windows.Controls.Button)(target));
            
            #line 20 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color2.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.Color3 = ((System.Windows.Controls.Button)(target));
            
            #line 21 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color3.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.Color4 = ((System.Windows.Controls.Button)(target));
            
            #line 22 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color4.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.Color5 = ((System.Windows.Controls.Button)(target));
            
            #line 23 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color5.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.Color6 = ((System.Windows.Controls.Button)(target));
            
            #line 24 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color6.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.Color7 = ((System.Windows.Controls.Button)(target));
            
            #line 25 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color7.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.Color8 = ((System.Windows.Controls.Button)(target));
            
            #line 26 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color8.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.Color9 = ((System.Windows.Controls.Button)(target));
            
            #line 28 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color9.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.Color10 = ((System.Windows.Controls.Button)(target));
            
            #line 29 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color10.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.Color11 = ((System.Windows.Controls.Button)(target));
            
            #line 30 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color11.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.Color12 = ((System.Windows.Controls.Button)(target));
            
            #line 31 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color12.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.Color13 = ((System.Windows.Controls.Button)(target));
            
            #line 32 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color13.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.Color14 = ((System.Windows.Controls.Button)(target));
            
            #line 33 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color14.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.Color15 = ((System.Windows.Controls.Button)(target));
            
            #line 34 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color15.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.Color16 = ((System.Windows.Controls.Button)(target));
            
            #line 35 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color16.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.Color17 = ((System.Windows.Controls.Button)(target));
            
            #line 37 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color17.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.Color18 = ((System.Windows.Controls.Button)(target));
            
            #line 38 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color18.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.Color19 = ((System.Windows.Controls.Button)(target));
            
            #line 39 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color19.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.Color20 = ((System.Windows.Controls.Button)(target));
            
            #line 40 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color20.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.Color21 = ((System.Windows.Controls.Button)(target));
            
            #line 41 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color21.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.Color22 = ((System.Windows.Controls.Button)(target));
            
            #line 42 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color22.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.Color23 = ((System.Windows.Controls.Button)(target));
            
            #line 43 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color23.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.Color24 = ((System.Windows.Controls.Button)(target));
            
            #line 44 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color24.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.Color25 = ((System.Windows.Controls.Button)(target));
            
            #line 46 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color25.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.Color26 = ((System.Windows.Controls.Button)(target));
            
            #line 47 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color26.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.Color27 = ((System.Windows.Controls.Button)(target));
            
            #line 48 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color27.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.Color28 = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color28.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.Color29 = ((System.Windows.Controls.Button)(target));
            
            #line 50 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color29.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.Color30 = ((System.Windows.Controls.Button)(target));
            
            #line 51 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color30.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.Color31 = ((System.Windows.Controls.Button)(target));
            
            #line 52 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color31.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.Color32 = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color32.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.Color33 = ((System.Windows.Controls.Button)(target));
            
            #line 55 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color33.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            this.Color34 = ((System.Windows.Controls.Button)(target));
            
            #line 56 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color34.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.Color35 = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color35.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.Color36 = ((System.Windows.Controls.Button)(target));
            
            #line 58 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color36.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            this.Color37 = ((System.Windows.Controls.Button)(target));
            
            #line 59 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color37.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            this.Color38 = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color38.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            this.Color39 = ((System.Windows.Controls.Button)(target));
            
            #line 61 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color39.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.Color40 = ((System.Windows.Controls.Button)(target));
            
            #line 62 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color40.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            this.Color41 = ((System.Windows.Controls.Button)(target));
            
            #line 64 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color41.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.Color42 = ((System.Windows.Controls.Button)(target));
            
            #line 65 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color42.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.Color43 = ((System.Windows.Controls.Button)(target));
            
            #line 66 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color43.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            this.Color44 = ((System.Windows.Controls.Button)(target));
            
            #line 67 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color44.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            this.Color45 = ((System.Windows.Controls.Button)(target));
            
            #line 68 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color45.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            this.Color46 = ((System.Windows.Controls.Button)(target));
            
            #line 69 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color46.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            this.Color47 = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color47.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 48:
            this.Color48 = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.Color48.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 49:
            this.colorPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 50:
            this.okButton = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.okButton.Click += new System.Windows.RoutedEventHandler(this.OkButton_Click);
            
            #line default
            #line hidden
            return;
            case 51:
            this.cancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 84 "..\..\..\..\Dialogs\ColorPickerDialog.xaml"
            this.cancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

