<Window x:Class="MemoryEnhancer.Dialogs.ColorPickerDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="选择颜色" Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 预设颜色面板 -->
        <GroupBox Header="预设颜色" Grid.Row="0" Margin="0,0,0,10">
            <UniformGrid Columns="8" Rows="6" Margin="10">
                <!-- 常用颜色 -->
                <Button Name="Color1" Background="Black" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color2" Background="White" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color3" Background="Red" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color4" Background="Green" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color5" Background="Blue" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color6" Background="Yellow" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color7" Background="Orange" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color8" Background="Purple" Click="ColorButton_Click" Margin="2" Height="30"/>
                
                <Button Name="Color9" Background="Gray" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color10" Background="LightGray" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color11" Background="Pink" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color12" Background="LightGreen" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color13" Background="LightBlue" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color14" Background="LightYellow" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color15" Background="Cyan" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color16" Background="Magenta" Click="ColorButton_Click" Margin="2" Height="30"/>
                
                <Button Name="Color17" Background="DarkRed" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color18" Background="DarkGreen" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color19" Background="DarkBlue" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color20" Background="Brown" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color21" Background="Navy" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color22" Background="Maroon" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color23" Background="Olive" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color24" Background="Teal" Click="ColorButton_Click" Margin="2" Height="30"/>
                
                <Button Name="Color25" Background="Silver" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color26" Background="Lime" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color27" Background="Aqua" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color28" Background="Fuchsia" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color29" Background="Gold" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color30" Background="Coral" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color31" Background="Violet" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color32" Background="Indigo" Click="ColorButton_Click" Margin="2" Height="30"/>
                
                <Button Name="Color33" Background="DarkGray" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color34" Background="LightCoral" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color35" Background="LightSeaGreen" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color36" Background="LightSkyBlue" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color37" Background="Khaki" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color38" Background="PaleGreen" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color39" Background="Plum" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color40" Background="SandyBrown" Click="ColorButton_Click" Margin="2" Height="30"/>
                
                <Button Name="Color41" Background="Wheat" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color42" Background="Thistle" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color43" Background="PowderBlue" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color44" Background="MistyRose" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color45" Background="LemonChiffon" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color46" Background="Lavender" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color47" Background="Honeydew" Click="ColorButton_Click" Margin="2" Height="30"/>
                <Button Name="Color48" Background="AliceBlue" Click="ColorButton_Click" Margin="2" Height="30"/>
            </UniformGrid>
        </GroupBox>

        <!-- 当前选择的颜色预览 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,10">
            <TextBlock Text="当前选择的颜色：" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <Rectangle Name="colorPreview" Width="50" Height="30" Stroke="Black" StrokeThickness="1"/>
        </StackPanel>

        <!-- 按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Name="okButton" Content="确定" Width="80" Height="30" Margin="0,0,10,0" Click="OkButton_Click" IsDefault="True"/>
            <Button Name="cancelButton" Content="取消" Width="80" Height="30" Click="CancelButton_Click" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
