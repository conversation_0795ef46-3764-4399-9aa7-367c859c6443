# 我要记忆 v1.0 - 安装和使用说明

## 📦 **安装包说明**

### **✅ 自包含便携版（推荐）**
- **文件名**：`我要记忆_v1.0_Windows10+_便携版.zip`
- **大小**：约 70MB（压缩后）
- **优点**：无需安装.NET运行时，解压即用
- **适用系统**：Windows 10 及以上版本

### **⚠️ 轻量版（需要.NET运行时）**
- **文件夹**：`publish-final`
- **大小**：约 20MB
- **缺点**：需要用户系统安装.NET 6.0运行时
- **适用场景**：已安装.NET 6.0的开发环境

## 🚀 **安装步骤**

### **方法一：便携版安装（推荐）**

1. **下载安装包**
   - 下载 `我要记忆_v1.0_Windows10+_便携版.zip`

2. **解压缩**
   - 右键点击zip文件 → 选择"解压到..."
   - 选择一个文件夹（如：`C:\Program Files\我要记忆\`）

3. **运行程序**
   - 进入解压后的文件夹
   - 双击 `MemoryEnhancer.exe` 启动程序

4. **创建桌面快捷方式（可选）**
   - 右键点击 `MemoryEnhancer.exe`
   - 选择"发送到" → "桌面快捷方式"

### **方法二：轻量版安装**

1. **安装.NET 6.0运行时**
   - 访问：https://dotnet.microsoft.com/download/dotnet/6.0
   - 下载并安装 ".NET Desktop Runtime 6.0.x"

2. **复制程序文件**
   - 将 `publish-final` 文件夹复制到目标位置

3. **运行程序**
   - 双击 `MemoryEnhancer.exe`

## 🎯 **系统要求**

### **最低要求**
- **操作系统**：Windows 10 版本 1809 (17763) 或更高
- **架构**：x64 (64位)
- **内存**：至少 512MB 可用内存
- **存储空间**：至少 300MB 可用空间

### **推荐配置**
- **操作系统**：Windows 10 版本 21H2 或 Windows 11
- **内存**：2GB 或更多
- **存储空间**：1GB 可用空间
- **音频**：支持语音合成的音频设备

## ✅ **功能验证**

### **启动测试**
1. 双击程序图标
2. 程序应该在几秒内启动
3. 窗口显示在屏幕左下角

### **基本功能测试**
1. **界面显示**：确认程序界面正常显示
2. **设置功能**：点击设置按钮，确认设置窗口打开
3. **语音功能**：在设置中测试语音朗读
4. **文件导入**：尝试导入一个文本文件

## 🔧 **常见问题解决**

### **程序无法启动**
- **检查系统版本**：确保是Windows 10 1809或更高版本
- **检查架构**：确保是64位系统
- **重新解压**：删除文件夹，重新解压zip文件
- **管理员权限**：右键以管理员身份运行

### **语音功能不工作**
- **检查音频设备**：确保系统有可用的音频输出设备
- **检查语音服务**：确保Windows语音识别服务正在运行
- **重启程序**：关闭程序后重新启动

### **文件导入失败**
- **检查文件格式**：支持TXT、CSV、JSON、Excel、Word、Markdown
- **检查文件权限**：确保文件没有被其他程序占用
- **检查文件编码**：建议使用UTF-8编码

## 📞 **技术支持**

### **日志文件位置**
- 程序运行时会在程序目录下生成日志文件
- 文件名格式：`log_YYYY-MM-DD.txt`
- 遇到问题时可查看日志文件获取详细错误信息

### **数据文件位置**
- 用户数据保存在程序目录下
- 包括：设置文件、记忆项目、学习进度等
- 卸载程序前请备份这些文件

## 🎉 **开始使用**

1. **首次运行**：程序会创建默认设置
2. **导入内容**：通过设置界面导入学习材料
3. **开始学习**：使用播放/暂停控制学习节奏
4. **个性化设置**：调整字体、颜色、语音等设置

**祝您学习愉快！** 🚀✨
