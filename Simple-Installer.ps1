# Memory Enhancer - Simple Installer
# Requires Administrator privileges

param(
    [string]$InstallPath = "$env:ProgramFiles\MemoryEnhancer"
)

# 确保窗口不会立即关闭
$Host.UI.RawUI.WindowTitle = "Memory Enhancer Installer"

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 清屏
Clear-Host

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This installer requires administrator privileges. Restarting..." -ForegroundColor Yellow
    Start-Process PowerShell -Verb RunAs "-File `"$PSCommandPath`" $args"
    exit
}

# Check system requirements
$OSVersion = [System.Environment]::OSVersion.Version
if ($OSVersion.Major -lt 10 -or ($OSVersion.Major -eq 10 -and $OSVersion.Build -lt 17763)) {
    Write-Host "System requirements not met" -ForegroundColor Red
    Write-Host "This program requires Windows 10 version 1809 (Build 17763) or higher" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

if (-not [Environment]::Is64BitOperatingSystem) {
    Write-Host "System architecture not supported" -ForegroundColor Red
    Write-Host "This program requires 64-bit system" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host "========================================" -ForegroundColor Green
Write-Host "Memory Enhancer - Installer" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Version: 1.0.0" -ForegroundColor White
Write-Host "Publisher: Zhou Shaowen" -ForegroundColor White
Write-Host "System Requirements: Windows 10+ (x64)" -ForegroundColor White
Write-Host ""

# Check if already installed
if (Test-Path "$InstallPath\MemoryEnhancer.exe") {
    Write-Host "Existing installation detected" -ForegroundColor Yellow
    $choice = Read-Host "Do you want to reinstall? (y/N)"
    if ($choice -ne 'y' -and $choice -ne 'Y') {
        Write-Host "Installation cancelled" -ForegroundColor Yellow
        exit 0
    }
}

Write-Host "Install path: $InstallPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "Installing to: $InstallPath" -ForegroundColor Green
Write-Host ""

try {
    # Create install directory
    if (-not (Test-Path $InstallPath)) {
        New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    }

    # Copy program files
    Write-Host "Copying program files..." -ForegroundColor Yellow

    $SourcePath = "publish-standalone"
    if (-not (Test-Path $SourcePath)) {
        Write-Host "Error: Source files not found at $SourcePath" -ForegroundColor Red
        exit 1
    }

    # Copy all files
    Copy-Item "$SourcePath\*" $InstallPath -Recurse -Force

    # Create shortcuts
    Write-Host "Creating shortcuts..." -ForegroundColor Yellow

    $WshShell = New-Object -comObject WScript.Shell

    # Desktop shortcut
    $DesktopShortcut = $WshShell.CreateShortcut("$env:PUBLIC\Desktop\Memory Enhancer.lnk")
    $DesktopShortcut.TargetPath = "$InstallPath\MemoryEnhancer.exe"
    $DesktopShortcut.WorkingDirectory = $InstallPath
    $DesktopShortcut.IconLocation = "$InstallPath\Resources\icon.ico"
    $DesktopShortcut.Description = "Memory Enhancer - Windows Memory Enhancement Program"
    $DesktopShortcut.Save()

    # Start menu shortcut
    $StartMenuShortcut = $WshShell.CreateShortcut("$env:ProgramData\Microsoft\Windows\Start Menu\Programs\Memory Enhancer.lnk")
    $StartMenuShortcut.TargetPath = "$InstallPath\MemoryEnhancer.exe"
    $StartMenuShortcut.WorkingDirectory = $InstallPath
    $StartMenuShortcut.IconLocation = "$InstallPath\Resources\icon.ico"
    $StartMenuShortcut.Description = "Memory Enhancer - Windows Memory Enhancement Program"
    $StartMenuShortcut.Save()

    # Register in system
    Write-Host "Registering program..." -ForegroundColor Yellow

    $UninstallKey = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\MemoryEnhancer"
    New-Item -Path $UninstallKey -Force | Out-Null
    Set-ItemProperty -Path $UninstallKey -Name "DisplayName" -Value "Memory Enhancer"
    Set-ItemProperty -Path $UninstallKey -Name "DisplayVersion" -Value "1.0.0"
    Set-ItemProperty -Path $UninstallKey -Name "Publisher" -Value "Zhou Shaowen"
    Set-ItemProperty -Path $UninstallKey -Name "InstallLocation" -Value $InstallPath
    Set-ItemProperty -Path $UninstallKey -Name "UninstallString" -Value "PowerShell -ExecutionPolicy Bypass -File `"$InstallPath\Uninstall.ps1`""
    Set-ItemProperty -Path $UninstallKey -Name "DisplayIcon" -Value "$InstallPath\Resources\icon.ico"
    Set-ItemProperty -Path $UninstallKey -Name "NoModify" -Value 1 -Type DWord
    Set-ItemProperty -Path $UninstallKey -Name "NoRepair" -Value 1 -Type DWord

    # Create uninstall script
    $UninstallScript = @'
# Memory Enhancer - Uninstaller
param([string]$InstallPath = "$env:ProgramFiles\MemoryEnhancer")

if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Start-Process PowerShell -Verb RunAs "-File `"$PSCommandPath`" $args"
    exit
}

Write-Host "Memory Enhancer - Uninstaller" -ForegroundColor Red
$confirm = Read-Host "Are you sure you want to uninstall Memory Enhancer? (y/N)"
if ($confirm -ne 'y' -and $confirm -ne 'Y') {
    exit 0
}

Write-Host "Uninstalling..." -ForegroundColor Yellow

# Stop processes
Get-Process -Name "MemoryEnhancer" -ErrorAction SilentlyContinue | Stop-Process -Force

# Remove shortcuts
Remove-Item "$env:PUBLIC\Desktop\Memory Enhancer.lnk" -Force -ErrorAction SilentlyContinue
Remove-Item "$env:ProgramData\Microsoft\Windows\Start Menu\Programs\Memory Enhancer.lnk" -Force -ErrorAction SilentlyContinue

# Remove registry
Remove-Item "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\MemoryEnhancer" -Force -ErrorAction SilentlyContinue

# Remove files
if (Test-Path $InstallPath) {
    Remove-Item $InstallPath -Recurse -Force
}

Write-Host "Uninstall completed" -ForegroundColor Green
Read-Host "Press any key to exit"
'@

    $UninstallScript | Out-File -FilePath "$InstallPath\Uninstall.ps1" -Encoding UTF8

    Write-Host ""
    Write-Host "Installation completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Program installed to: $InstallPath" -ForegroundColor White
    Write-Host "Desktop shortcut: Memory Enhancer.lnk" -ForegroundColor White
    Write-Host "Start menu: Memory Enhancer" -ForegroundColor White
    Write-Host ""

    $runNow = Read-Host "Do you want to run the program now? (Y/n)"
    if ($runNow -ne 'n' -and $runNow -ne 'N') {
        Start-Process "$InstallPath\MemoryEnhancer.exe"
    }

} catch {
    Write-Host "Installation failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Press any key to exit..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit 1
}

Write-Host ""
Write-Host "Installation completed successfully!" -ForegroundColor Green
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
