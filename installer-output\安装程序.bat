@echo off
chcp 65001 >nul
title 我要记忆 - 安装程序

echo ========================================
echo 我要记忆 v1.0 - 安装程序
echo ========================================
echo.
echo 正在启动安装程序...
echo 如果出现安全提示，请选择"是"或"允许"
echo.

:: 检查是否以管理员权限运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限，正在启动安装程序...
    echo.
) else (
    echo 需要管理员权限，正在请求权限...
    echo.
    :: 请求管理员权限重新运行
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

:: 启动PowerShell安装程序
powershell -ExecutionPolicy Bypass -File "%~dp0Install-MemoryEnhancer-Fixed.ps1"

echo.
echo 安装程序已完成
pause
