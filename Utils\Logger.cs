using System;
using System.IO;

namespace MemoryEnhancer.Utils
{
    /// <summary>
    /// 简单的日志记录器
    /// </summary>
    public static class Logger
    {
        private static readonly string LogDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            Constants.AppDataFolderName, Constants.LogsFolderName);

        private static readonly string LogFileName = $"{Constants.AppDataFolderName}_{DateTime.Now:yyyyMMdd}.log";
        private static readonly string LogFilePath = Path.Combine(LogDirectory, LogFileName);

        static Logger()
        {
            try
            {
                if (!Directory.Exists(LogDirectory))
                {
                    Directory.CreateDirectory(LogDirectory);
                }
            }
            catch
            {
                // 如果无法创建日志目录，静默失败
            }
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void Info(string message)
        {
            WriteLog("INFO", message);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void Warning(string message)
        {
            WriteLog("WARN", message);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void Error(string message)
        {
            WriteLog("ERROR", message);
        }

        /// <summary>
        /// 记录错误日志（包含异常信息）
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常对象</param>
        public static void Error(string message, Exception exception)
        {
            WriteLog("ERROR", $"{message}\nException: {exception}");
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        private static void WriteLog(string level, string message)
        {
            try
            {
                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{level}] {message}";

                // 同时输出到调试控制台和文件
                System.Diagnostics.Debug.WriteLine(logEntry);

                File.AppendAllText(LogFilePath, logEntry + Environment.NewLine);
            }
            catch
            {
                // 如果日志记录失败，静默失败，不影响主程序运行
            }
        }

        /// <summary>
        /// 清理旧的日志文件（保留最近指定天数的日志）
        /// </summary>
        public static void CleanupOldLogs()
        {
            try
            {
                if (!Directory.Exists(LogDirectory))
                    return;

                var cutoffDate = DateTime.Now.AddDays(-Constants.LogRetentionDays);
                var logFiles = Directory.GetFiles(LogDirectory, $"{Constants.AppDataFolderName}_*.log");

                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                    }
                }
            }
            catch
            {
                // 清理失败时静默处理
            }
        }
    }
}
