using System;
using System.Collections.Generic;
using System.Linq;
using MemoryEnhancer.Utils;

namespace MemoryEnhancer
{
    public enum Difficulty
    {
        Easy,
        Medium,
        Hard
    }

    public enum RepetitionAlgorithm
    {
        Simple,
        SuperMemo2,
        SuperMemo5
    }

    public class MemoryItem
    {
        public string Content { get; set; } = string.Empty;
        public Difficulty Difficulty { get; set; } = Difficulty.Medium;
        public DateTime LastReviewed { get; set; }
        public DateTime NextReviewDate { get; set; }
        public int ReviewCount { get; set; } = 0;
        public TimeSpan TotalStudyTime { get; set; } = TimeSpan.Zero;
        public DateTime StudyStartTime { get; set; }

        // SuperMemo算法参数
        public double EaseFactor { get; set; } = Constants.DefaultEaseFactor; // 默认难度系数
        public int Interval { get; set; } = 0; // 间隔天数
        public List<DateTime> ReviewHistory { get; set; } = new List<DateTime>();
        public List<TimeSpan> StudySessionTimes { get; set; } = new List<TimeSpan>();

        public MemoryItem()
        {
            // 默认构造函数，用于序列化
            LastReviewed = DateTime.Now;
            NextReviewDate = DateTime.Now;
            StudyStartTime = DateTime.Now;
        }

        public MemoryItem(string content)
        {
            Content = content;
            LastReviewed = DateTime.Now;
            StudyStartTime = DateTime.Now;
            // 新导入的项目应该立即可用于学习，设置NextReviewDate为今天
            NextReviewDate = DateTime.Today;
        }

        public void StartStudySession()
        {
            StudyStartTime = DateTime.Now;
        }

        public void EndStudySession()
        {
            if (StudyStartTime != DateTime.MinValue)
            {
                TimeSpan sessionTime = DateTime.Now - StudyStartTime;
                TotalStudyTime += sessionTime;
                StudySessionTimes.Add(sessionTime);
                StudyStartTime = DateTime.MinValue;
            }
        }

        public void CalculateNextReviewDate(RepetitionAlgorithm algorithm = RepetitionAlgorithm.Simple)
        {
            switch (algorithm)
            {
                case RepetitionAlgorithm.Simple:
                    CalculateSimpleNextReviewDate();
                    break;
                case RepetitionAlgorithm.SuperMemo2:
                    CalculateSuperMemo2NextReviewDate();
                    break;
                case RepetitionAlgorithm.SuperMemo5:
                    CalculateSuperMemo5NextReviewDate();
                    break;
            }

            ReviewCount++;
            ReviewHistory.Add(DateTime.Now);
        }

        private void CalculateSimpleNextReviewDate()
        {
            // 基于简单间隔重复算法计算下次复习日期
            int daysToAdd = 1;

            switch (Difficulty)
            {
                case Difficulty.Easy:
                    daysToAdd = (int)Math.Pow(2, ReviewCount);
                    break;
                case Difficulty.Medium:
                    daysToAdd = (int)Math.Pow(1.5, ReviewCount);
                    break;
                case Difficulty.Hard:
                    daysToAdd = 1; // 困难项目每天复习
                    break;
            }

            NextReviewDate = DateTime.Now.AddDays(daysToAdd);
        }

        private void CalculateSuperMemo2NextReviewDate()
        {
            // SuperMemo 2算法实现
            if (ReviewCount == 0)
            {
                Interval = 1;
            }
            else if (ReviewCount == 1)
            {
                Interval = 6;
            }
            else
            {
                // 根据难度调整间隔
                switch (Difficulty)
                {
                    case Difficulty.Easy:
                        Interval = (int)(Interval * EaseFactor);
                        break;
                    case Difficulty.Medium:
                        Interval = (int)(Interval * 1.5);
                        break;
                    case Difficulty.Hard:
                        Interval = 1; // 困难项目重置为1天
                        break;
                }
            }

            NextReviewDate = DateTime.Now.AddDays(Interval);
        }

        private void CalculateSuperMemo5NextReviewDate()
        {
            // SuperMemo 5算法实现 (SM-5)
            int quality = 0;

            switch (Difficulty)
            {
                case Difficulty.Easy:
                    quality = 5; // 完全记住
                    break;
                case Difficulty.Medium:
                    quality = 3; // 记住但有困难
                    break;
                case Difficulty.Hard:
                    quality = 1; // 几乎忘记
                    break;
            }

            // 首次学习
            if (ReviewCount == 0)
            {
                Interval = 1;
            }
            // 第二次复习
            else if (ReviewCount == 1)
            {
                Interval = 6;
            }
            else
            {
                // 更新EaseFactor (SM-5算法)
                EaseFactor = Math.Max(Constants.MinEaseFactor, EaseFactor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02)));

                // 更新间隔
                Interval = (int)(Interval * EaseFactor);
            }

            // 根据质量调整
            if (quality < 3)
            {
                Interval = 1; // 如果记忆质量低，重置为1天
            }

            NextReviewDate = DateTime.Now.AddDays(Interval);
        }

        public void MarkAsReviewed(Difficulty newDifficulty, RepetitionAlgorithm algorithm = RepetitionAlgorithm.Simple)
        {
            EndStudySession(); // 结束当前学习会话，记录学习时间
            Difficulty = newDifficulty;
            LastReviewed = DateTime.Now;
            CalculateNextReviewDate(algorithm);
        }
    }

    // 文件分组类，用于管理导入的文件
    public class MemoryFileGroup
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string OriginalFileName { get; set; } = string.Empty;
        public DateTime ImportDate { get; set; } = DateTime.Now;
        public List<MemoryItem> Items { get; set; } = new List<MemoryItem>();
        public int LastPlayedIndex { get; set; } = 0;

        public MemoryFileGroup()
        {
            // 默认构造函数，用于序列化
        }

        public MemoryFileGroup(string name, string originalFileName)
        {
            Name = name;
            OriginalFileName = originalFileName;
        }

        public MemoryFileGroup(string name, string originalFileName, List<MemoryItem> items)
        {
            Name = name;
            OriginalFileName = originalFileName;
            Items = items ?? new List<MemoryItem>();
        }

        // 获取文件组的统计信息
        public string GetStatistics()
        {
            int totalItems = Items.Count;
            int studiedItems = Items.Count(i => i.ReviewCount > 0);
            return $"{studiedItems}/{totalItems} 已学习";
        }

        // 获取今天需要学习的项目
        public List<MemoryItem> GetTodayItems(UserSettings settings)
        {
            var today = DateTime.Today;
            var todayItems = new List<MemoryItem>();

            // 获取新项目（ReviewCount == 0且NextReviewDate <= 今天）
            var newItems = Items
                .Where(i => i.ReviewCount == 0 && i.NextReviewDate.Date <= today)
                .Take(Math.Max(0, settings.DailyNewItemsLimit - settings.TodayNewItemsStudied))
                .ToList();

            // 获取今天应该复习的项目（排除新项目）
            var reviewItems = Items
                .Where(i => i.ReviewCount > 0 && i.NextReviewDate.Date <= today)
                .OrderBy(i => i.NextReviewDate)
                .Take(Math.Max(0, settings.DailyReviewItemsLimit - settings.TodayReviewItemsStudied))
                .ToList();

            // 合并新项目和复习项目
            todayItems.AddRange(newItems);
            todayItems.AddRange(reviewItems);

            // 如果今天的项目数量不足，且还有未学习的新项目，添加更多新项目
            if (todayItems.Count < Constants.MinDailyItemsCount && Items.Any(i => i.ReviewCount == 0))
            {
                var additionalNewItems = Items
                    .Where(i => i.ReviewCount == 0 && !todayItems.Contains(i))
                    .Take(Constants.MinDailyItemsCount - todayItems.Count)
                    .ToList();

                todayItems.AddRange(additionalNewItems);
            }

            // 为每个项目启动学习会话
            foreach (var item in todayItems)
            {
                item.StartStudySession();
            }

            return todayItems;
        }

        // 获取所有项目（用于完整播放）
        public List<MemoryItem> GetAllItems()
        {
            // 为每个项目启动学习会话
            foreach (var item in Items)
            {
                item.StartStudySession();
            }

            return Items.ToList();
        }
    }
}