# Simple ICO creation using .NET Icon class
Write-Host "Creating simple ICO file..." -ForegroundColor Green

try {
    Add-Type -AssemblyName System.Drawing
    
    # Create a 32x32 bitmap
    $size = 32
    $bitmap = New-Object System.Drawing.Bitmap($size, $size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Set high quality
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    
    # Draw background circle (blue)
    $brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(74, 144, 226))
    $graphics.FillEllipse($brush, 2, 2, 28, 28)
    
    # Draw brain outline (white)
    $pen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, 2)
    $graphics.DrawEllipse($pen, 6, 10, 8, 10)   # Left brain
    $graphics.DrawEllipse($pen, 18, 10, 8, 10)  # Right brain
    $graphics.DrawLine($pen, 16, 10, 16, 20)    # Center line
    
    # Draw lightbulb (gold)
    $goldBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 215, 0))
    $graphics.FillEllipse($goldBrush, 13, 4, 6, 8)
    
    # Draw base
    $grayBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(200, 200, 200))
    $graphics.FillRectangle($grayBrush, 14, 11, 4, 2)
    
    $graphics.Dispose()
    
    # Convert to icon
    $hIcon = $bitmap.GetHicon()
    $icon = [System.Drawing.Icon]::FromHandle($hIcon)
    
    # Save as ICO
    $iconPath = "Resources\icon.ico"
    $fileStream = [System.IO.File]::Create($iconPath)
    $icon.Save($fileStream)
    $fileStream.Close()
    
    # Cleanup
    $icon.Dispose()
    $bitmap.Dispose()
    $brush.Dispose()
    $pen.Dispose()
    $goldBrush.Dispose()
    $grayBrush.Dispose()
    
    Write-Host "ICO file created successfully: $iconPath" -ForegroundColor Green
    Write-Host "File size: $((Get-Item $iconPath).Length) bytes" -ForegroundColor Cyan
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
