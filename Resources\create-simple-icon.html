<!DOCTYPE html>
<html>
<head>
    <title>我要记忆图标生成器</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        canvas { border: 1px solid #ccc; margin: 20px 0; }
        button { padding: 10px 20px; margin: 5px; background: #4A90E2; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #357ABD; }
    </style>
</head>
<body>
    <h1>我要记忆图标生成器</h1>
    <p>这个工具为我要记忆应用程序创建简单的图标。</p>

    <canvas id="iconCanvas" width="256" height="256"></canvas>
    <br>
    <button onclick="generateIcon()">Generate Icon</button>
    <button onclick="downloadIcon()">Download PNG</button>

    <p><strong>Instructions:</strong></p>
    <ol>
        <li>Click "Generate Icon" to create the icon</li>
        <li>Click "Download PNG" to save the image</li>
        <li>Use an online converter to convert PNG to ICO format</li>
        <li>Save the ICO file as "Resources/icon.ico"</li>
    </ol>

    <script>
        function generateIcon() {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');

            // Clear canvas
            ctx.clearRect(0, 0, 256, 256);

            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, 256, 256);
            gradient.addColorStop(0, '#4A90E2');
            gradient.addColorStop(0.5, '#357ABD');
            gradient.addColorStop(1, '#2E5F8A');

            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(128, 128, 120, 0, 2 * Math.PI);
            ctx.fill();

            // Draw brain outline
            ctx.strokeStyle = '#FFFFFF';
            ctx.lineWidth = 4;

            // Left brain hemisphere
            ctx.beginPath();
            ctx.arc(90, 128, 40, 0, 2 * Math.PI);
            ctx.stroke();

            // Right brain hemisphere
            ctx.beginPath();
            ctx.arc(166, 128, 40, 0, 2 * Math.PI);
            ctx.stroke();

            // Central line
            ctx.beginPath();
            ctx.moveTo(128, 80);
            ctx.lineTo(128, 176);
            ctx.stroke();

            // Draw lightbulb (memory symbol)
            ctx.fillStyle = '#FFD700';
            ctx.beginPath();
            ctx.arc(128, 60, 12, 0, 2 * Math.PI);
            ctx.fill();

            // Lightbulb base
            ctx.fillStyle = '#E0E0E0';
            ctx.fillRect(120, 70, 16, 8);

            // Memory dots
            ctx.fillStyle = '#FFA500';
            ctx.beginPath();
            ctx.arc(70, 110, 4, 0, 2 * Math.PI);
            ctx.fill();

            ctx.beginPath();
            ctx.arc(186, 110, 4, 0, 2 * Math.PI);
            ctx.fill();

            ctx.beginPath();
            ctx.arc(90, 150, 3, 0, 2 * Math.PI);
            ctx.fill();

            ctx.beginPath();
            ctx.arc(166, 150, 3, 0, 2 * Math.PI);
            ctx.fill();

            // Add light rays
            ctx.strokeStyle = '#FFD700';
            ctx.lineWidth = 2;

            // Top ray
            ctx.beginPath();
            ctx.moveTo(128, 35);
            ctx.lineTo(128, 45);
            ctx.stroke();

            // Left ray
            ctx.beginPath();
            ctx.moveTo(105, 45);
            ctx.lineTo(110, 50);
            ctx.stroke();

            // Right ray
            ctx.beginPath();
            ctx.moveTo(151, 45);
            ctx.lineTo(146, 50);
            ctx.stroke();
        }

        function downloadIcon() {
            const canvas = document.getElementById('iconCanvas');
            const link = document.createElement('a');
            link.download = '我要记忆-icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        // Generate icon on page load
        window.onload = generateIcon;
    </script>
</body>
</html>
