# PowerShell Script: Create ICO file for Memory Enhancer
# This script creates a proper ICO file with multiple sizes

Write-Host "Creating Memory Enhancer ICO file..." -ForegroundColor Green

# Create Resources directory if it doesn't exist
$resourcesDir = "Resources"
if (!(Test-Path $resourcesDir)) {
    New-Item -ItemType Directory -Path $resourcesDir -Force
    Write-Host "Created Resources directory" -ForegroundColor Yellow
}

try {
    # Load required assemblies
    Add-Type -AssemblyName System.Drawing
    Add-Type -AssemblyName System.Windows.Forms
    
    Write-Host "System.Drawing loaded successfully" -ForegroundColor Green
    
    # Function to create icon bitmap at specific size
    function Create-IconBitmap($size) {
        $bitmap = New-Object System.Drawing.Bitmap($size, $size)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Set high quality rendering
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
        $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        
        # Calculate proportional sizes
        $center = $size / 2
        $radius = [Math]::Floor($size * 0.45)
        $brainRadius = [Math]::Floor($size * 0.15)
        $lightbulbSize = [Math]::Floor($size * 0.12)
        
        # Create gradient background
        $rect = New-Object System.Drawing.Rectangle(($center - $radius), ($center - $radius), ($radius * 2), ($radius * 2))
        $brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush($rect, 
            [System.Drawing.Color]::FromArgb(74, 144, 226), 
            [System.Drawing.Color]::FromArgb(46, 95, 138), 
            [System.Drawing.Drawing2D.LinearGradientMode]::Diagonal)
        $graphics.FillEllipse($brush, $rect)
        
        # Draw brain outline (white)
        $pen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, [Math]::Max(1, $size / 32))
        
        # Left brain hemisphere
        $leftBrain = New-Object System.Drawing.Rectangle(
            ($center - $brainRadius * 1.8), 
            ($center - $brainRadius * 0.8), 
            ($brainRadius * 1.6), 
            ($brainRadius * 1.6))
        $graphics.DrawEllipse($pen, $leftBrain)
        
        # Right brain hemisphere  
        $rightBrain = New-Object System.Drawing.Rectangle(
            ($center + $brainRadius * 0.2), 
            ($center - $brainRadius * 0.8), 
            ($brainRadius * 1.6), 
            ($brainRadius * 1.6))
        $graphics.DrawEllipse($pen, $rightBrain)
        
        # Central line
        $graphics.DrawLine($pen, $center, ($center - $brainRadius), $center, ($center + $brainRadius))
        
        # Draw lightbulb (golden)
        $lightBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 215, 0))
        $lightRect = New-Object System.Drawing.Rectangle(
            ($center - $lightbulbSize/2), 
            ($center - $radius * 0.7), 
            $lightbulbSize, 
            ($lightbulbSize * 1.2))
        $graphics.FillEllipse($lightBrush, $lightRect)
        
        # Lightbulb base
        $baseBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(224, 224, 224))
        $baseRect = New-Object System.Drawing.Rectangle(
            ($center - $lightbulbSize/3), 
            ($center - $radius * 0.5), 
            ($lightbulbSize * 0.66), 
            ($lightbulbSize * 0.3))
        $graphics.FillRectangle($baseBrush, $baseRect)
        
        # Memory dots (orange)
        if ($size -ge 32) {
            $dotBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 165, 0))
            $dotSize = [Math]::Max(2, $size / 16)
            
            $graphics.FillEllipse($dotBrush, ($center - $brainRadius * 1.2), ($center - $brainRadius * 0.2), $dotSize, $dotSize)
            $graphics.FillEllipse($dotBrush, ($center + $brainRadius * 0.8), ($center - $brainRadius * 0.2), $dotSize, $dotSize)
            
            if ($size -ge 48) {
                $graphics.FillEllipse($dotBrush, ($center - $brainRadius * 0.8), ($center + $brainRadius * 0.4), ($dotSize * 0.8), ($dotSize * 0.8))
                $graphics.FillEllipse($dotBrush, ($center + $brainRadius * 0.4), ($center + $brainRadius * 0.4), ($dotSize * 0.8), ($dotSize * 0.8))
            }
            
            $dotBrush.Dispose()
        }
        
        # Clean up
        $graphics.Dispose()
        $brush.Dispose()
        $pen.Dispose()
        $lightBrush.Dispose()
        $baseBrush.Dispose()
        
        return $bitmap
    }
    
    # Create bitmaps for different sizes
    $sizes = @(16, 32, 48, 64, 128, 256)
    $bitmaps = @()
    
    foreach ($size in $sizes) {
        Write-Host "Creating ${size}x${size} icon..." -ForegroundColor Cyan
        $bitmap = Create-IconBitmap $size
        $bitmaps += $bitmap
        
        # Save individual PNG for reference
        $pngPath = "$resourcesDir\icon_${size}x${size}.png"
        $bitmap.Save($pngPath, [System.Drawing.Imaging.ImageFormat]::Png)
    }
    
    Write-Host "Created individual PNG files" -ForegroundColor Green
    
    # Create ICO file manually (simplified approach)
    # Since .NET doesn't have direct ICO creation, we'll use the largest bitmap and save it
    $mainBitmap = $bitmaps[-1]  # 256x256 bitmap
    
    # Convert to icon format using a workaround
    $iconPath = "$resourcesDir\icon.ico"
    
    # Method 1: Try to use Icon.FromHandle (may not work in all environments)
    try {
        $hIcon = $mainBitmap.GetHicon()
        $icon = [System.Drawing.Icon]::FromHandle($hIcon)
        
        # Save icon
        $fileStream = [System.IO.File]::Create($iconPath)
        $icon.Save($fileStream)
        $fileStream.Close()
        
        # Clean up
        $icon.Dispose()
        [System.Runtime.InteropServices.Marshal]::DestroyIcon($hIcon)
        
        Write-Host "ICO file created successfully: $iconPath" -ForegroundColor Green
        $icoCreated = $true
    }
    catch {
        Write-Host "Method 1 failed: $($_.Exception.Message)" -ForegroundColor Yellow
        $icoCreated = $false
    }
    
    # Method 2: If Method 1 fails, create a simple ICO using bitmap
    if (-not $icoCreated) {
        try {
            # Use 32x32 bitmap for compatibility
            $iconBitmap = $bitmaps[1]  # 32x32
            $hIcon = $iconBitmap.GetHicon()
            $icon = [System.Drawing.Icon]::FromHandle($hIcon)
            
            $fileStream = [System.IO.File]::Create($iconPath)
            $icon.Save($fileStream)
            $fileStream.Close()
            
            $icon.Dispose()
            [System.Runtime.InteropServices.Marshal]::DestroyIcon($hIcon)
            
            Write-Host "ICO file created successfully (Method 2): $iconPath" -ForegroundColor Green
            $icoCreated = $true
        }
        catch {
            Write-Host "Method 2 also failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # Clean up bitmaps
    foreach ($bitmap in $bitmaps) {
        $bitmap.Dispose()
    }
    
    if ($icoCreated) {
        Write-Host "`nICO file creation completed successfully!" -ForegroundColor Green
        Write-Host "Location: $iconPath" -ForegroundColor White
        Write-Host "Size: $((Get-Item $iconPath).Length) bytes" -ForegroundColor White
    } else {
        Write-Host "`nICO file creation failed. Using PNG as fallback." -ForegroundColor Yellow
        Write-Host "You can use online converters to create ICO from the PNG files." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Error creating ICO file: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please use online ICO converter tools instead." -ForegroundColor Yellow
}

Write-Host "`n=== Icon Creation Summary ===" -ForegroundColor Cyan
Write-Host "✓ PNG files created for all sizes (16x16 to 256x256)" -ForegroundColor Green
Write-Host "✓ ICO file creation attempted" -ForegroundColor Green
Write-Host "✓ Files saved in Resources directory" -ForegroundColor Green

Write-Host "`nNext steps:" -ForegroundColor White
Write-Host "1. Check the Resources directory for generated files" -ForegroundColor Gray
Write-Host "2. If ICO creation failed, use online converter with icon_256x256.png" -ForegroundColor Gray
Write-Host "3. Update project configuration to use the ICO file" -ForegroundColor Gray
