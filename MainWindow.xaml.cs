

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using System.Speech.Synthesis;
using MemoryEnhancer.Exceptions;
using MemoryEnhancer.Utils;


namespace MemoryEnhancer
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : System.Windows.Window
    {
        private List<MemoryItem> memoryItems = new();
        private int currentIndex = 0;
        private SpeechSynthesizer? synthesizer = null;
        private DispatcherTimer intervalTimer = new(); // 用于两条内容之间的间隔时间
        private readonly DispatcherTimer saveTimer = new(); // 用于延迟保存数据
        private UserSettings settings = new();
        private bool isSpeaking = false;
        private bool isReadingStopped = false; // 用户是否主动停止了朗读功能
        private bool hasUnsavedChanges = false; // 是否有未保存的更改
        private bool isTtsAvailable = false; // TTS功能是否可用
        private int currentRepeatCount = 0; // 当前重复次数
        private int targetRepeatCount = 1; // 目标重复次数

        public MainWindow()
        {
            try
            {
                // WPF自动生成的InitializeComponent方法调用
                // 使用反射调用以避免IDE误报
                var initMethod = this.GetType().GetMethod("InitializeComponent",
                    System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                initMethod?.Invoke(this, null);

                // 清理旧日志文件
                Logger.CleanupOldLogs();
                Logger.Info("应用程序启动");

                // 初始化语音合成器
                InitializeTTS();

                // 加载用户设置
                LoadSettings();

                // 文件组选择现在在设置对话框中管理

                // 初始化显示计时器
                InitializeTimer();

                // 初始化保存计时器
                InitializeSaveTimer();

                // 加载记忆内容
                LoadMemoryItems();

                // 显示第一条内容
                DisplayCurrentItem();

                // 启用键盘事件处理
                KeyDown += MainWindow_KeyDown;
                Focusable = true;

                // 设置窗口位置到左下角
                SetWindowPositionToBottomLeft();

                // 设置窗口图标
                SetWindowIcon();

                Logger.Info("应用程序初始化完成");
            }
            catch (Exception ex)
            {
                Logger.Error("应用程序初始化失败", ex);
                MessageBox.Show($"应用程序初始化失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // 尝试优雅关闭
                Application.Current.Shutdown();
            }
        }

        // 文件组选择功能已移到设置对话框中

        /// <summary>
        /// 设置窗口位置到左下角
        /// </summary>
        private void SetWindowPositionToBottomLeft()
        {
            try
            {
                // 获取屏幕工作区域（排除任务栏）
                var workingArea = SystemParameters.WorkArea;

                // 设置窗口位置：左边距10像素，底部距离10像素
                Left = 10;
                Top = workingArea.Height - ActualHeight - 10;

                // 如果窗口还没有渲染完成，使用Loaded事件来设置位置
                if (ActualHeight == 0)
                {
                    Loaded += (sender, e) =>
                    {
                        Top = workingArea.Height - ActualHeight - 10;
                    };
                }
            }
            catch (Exception ex)
            {
                Logger.Warning($"设置窗口位置时出错: {ex.Message}");
                // 如果设置失败，使用默认位置
                Left = 10;
                Top = 100;
            }
        }

        /// <summary>
        /// 设置窗口图标
        /// </summary>
        private void SetWindowIcon()
        {
            try
            {
                // 创建一个简单的内存图标
                var iconBitmap = CreateMemoryIcon();
                if (iconBitmap != null)
                {
                    Icon = iconBitmap;
                    Logger.Info("窗口图标设置成功");
                }
            }
            catch (Exception ex)
            {
                Logger.Warning($"设置窗口图标时出错: {ex.Message}");
                // 图标设置失败不影响程序运行
            }
        }

        /// <summary>
        /// 创建我要记忆图标
        /// </summary>
        /// <returns>图标位图源</returns>
        private BitmapSource? CreateMemoryIcon()
        {
            try
            {
                // 创建一个32x32的图标
                var drawingVisual = new DrawingVisual();
                using (var drawingContext = drawingVisual.RenderOpen())
                {
                    // 背景圆形（蓝色渐变）
                    var gradient = new LinearGradientBrush();
                    gradient.StartPoint = new Point(0, 0);
                    gradient.EndPoint = new Point(1, 1);
                    gradient.GradientStops.Add(new GradientStop(Color.FromRgb(74, 144, 226), 0));
                    gradient.GradientStops.Add(new GradientStop(Color.FromRgb(46, 95, 138), 1));

                    drawingContext.DrawEllipse(gradient, null, new Point(16, 16), 15, 15);

                    // 大脑轮廓（白色）
                    var whitePen = new Pen(Brushes.White, 1.5);

                    // 左脑半球
                    drawingContext.DrawEllipse(null, whitePen, new Point(10, 16), 6, 8);
                    // 右脑半球
                    drawingContext.DrawEllipse(null, whitePen, new Point(22, 16), 6, 8);
                    // 中央分割线
                    drawingContext.DrawLine(whitePen, new Point(16, 8), new Point(16, 24));

                    // 记忆灯泡（金色）
                    var goldBrush = new SolidColorBrush(Color.FromRgb(255, 215, 0));
                    drawingContext.DrawEllipse(goldBrush, null, new Point(16, 6), 3, 4);

                    // 灯泡底座
                    var grayBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224));
                    drawingContext.DrawRectangle(grayBrush, null, new Rect(14, 9, 4, 2));

                    // 记忆点
                    var orangeBrush = new SolidColorBrush(Color.FromRgb(255, 165, 0));
                    drawingContext.DrawEllipse(orangeBrush, null, new Point(8, 14), 1, 1);
                    drawingContext.DrawEllipse(orangeBrush, null, new Point(24, 14), 1, 1);
                }

                // 渲染为位图
                var renderTargetBitmap = new RenderTargetBitmap(32, 32, 96, 96, PixelFormats.Pbgra32);
                renderTargetBitmap.Render(drawingVisual);

                return renderTargetBitmap;
            }
            catch (Exception ex)
            {
                Logger.Warning($"创建图标时出错: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 初始化TTS语音合成器，包含Windows 7兼容性检查
        /// </summary>
        private void InitializeTTS()
        {
            try
            {
                synthesizer = new SpeechSynthesizer();
                synthesizer.SpeakCompleted += Synthesizer_SpeakCompleted;

                // 测试TTS功能是否可用
                var voices = synthesizer.GetInstalledVoices();
                if (voices.Count > 0)
                {
                    isTtsAvailable = true;
                    Logger.Info($"TTS初始化成功，可用语音数量: {voices.Count}");

                    // 在Windows 7上设置默认语音
                    try
                    {
                        // 尝试选择中文语音，如果没有则使用默认语音
                        var chineseVoice = voices.FirstOrDefault(v =>
                            v.VoiceInfo.Culture.Name.StartsWith("zh") ||
                            v.VoiceInfo.Name.Contains("Chinese") ||
                            v.VoiceInfo.Name.Contains("中文"));

                        if (chineseVoice != null)
                        {
                            synthesizer.SelectVoice(chineseVoice.VoiceInfo.Name);
                            Logger.Info($"选择语音: {chineseVoice.VoiceInfo.Name}");
                        }

                        // 应用语音速度设置
                        ApplySpeechSettings();
                    }
                    catch (Exception ex)
                    {
                        Logger.Warning($"设置语音时出错，使用默认语音: {ex.Message}");
                    }
                }
                else
                {
                    isTtsAvailable = false;
                    Logger.Warning("系统中没有可用的TTS语音");
                }
            }
            catch (Exception ex)
            {
                isTtsAvailable = false;
                Logger.Error("TTS初始化失败", ex);

                // 在Windows 7上可能出现的常见问题
                string errorMessage = "语音合成功能初始化失败。";
                if (ex.Message.Contains("SAPI") || ex.Message.Contains("Speech"))
                {
                    errorMessage += "\n\n可能的解决方案：\n" +
                                  "1. 确保Windows语音识别服务正在运行\n" +
                                  "2. 检查系统是否安装了语音包\n" +
                                  "3. 尝试重启程序或重启计算机";
                }

                MessageBox.Show(errorMessage, "语音功能提示",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 应用语音设置（速度等）
        /// </summary>
        private void ApplySpeechSettings()
        {
            if (synthesizer != null && isTtsAvailable)
            {
                try
                {
                    // 设置语音速度，范围是-10到10
                    synthesizer.Rate = settings.SpeechRate;
                    Logger.Info($"应用语音速度设置: {settings.SpeechRate}");
                }
                catch (Exception ex)
                {
                    Logger.Warning($"设置语音速度时出错: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 处理文本以添加智能停顿
        /// </summary>
        /// <param name="text">原始文本</param>
        /// <returns>处理后的文本</returns>
        private string ProcessTextForSmartPause(string text)
        {
            if (!settings.EnableSmartPause || string.IsNullOrEmpty(text))
                return text;

            var result = text;

            try
            {
                // 使用空格和特殊字符来模拟停顿，而不是SSML标记
                // 在句号、问号、感叹号后添加长停顿（3个空格）
                result = result.Replace("。", "。   ");
                result = result.Replace("？", "？   ");
                result = result.Replace("！", "！   ");
                result = result.Replace(".", ".   ");
                result = result.Replace("?", "?   ");
                result = result.Replace("!", "!   ");

                // 在逗号、分号后添加中等停顿（2个空格）
                result = result.Replace("，", "，  ");
                result = result.Replace("；", "；  ");
                result = result.Replace(",", ",  ");
                result = result.Replace(";", ";  ");

                // 在顿号、冒号后添加短停顿（1个空格）
                result = result.Replace("、", "、 ");
                result = result.Replace("：", "： ");
                result = result.Replace(":", ": ");

                // 处理连续空格：将多个空格替换为更多空格来增加停顿感
                for (int i = 5; i >= 2; i--)
                {
                    string spaces = new string(' ', i);
                    string longerSpaces = new string(' ', i + 2); // 增加2个空格
                    result = result.Replace(spaces, longerSpaces);
                }

                Logger.Info($"智能停顿处理完成，原文本长度: {text.Length}, 处理后长度: {result.Length}");
            }
            catch (Exception ex)
            {
                Logger.Warning($"智能停顿处理时出错: {ex.Message}");
                return text; // 出错时返回原文本
            }

            return result;
        }

        private void InitializeSaveTimer()
        {
            // 保存计时器：延迟指定时间后保存数据，避免频繁磁盘操作
            saveTimer.Interval = TimeSpan.FromSeconds(Constants.DelaySaveTimeSeconds);
            saveTimer.Tick += SaveTimer_Tick;
        }

        private void SaveTimer_Tick(object? sender, EventArgs e)
        {
            saveTimer.Stop();
            if (hasUnsavedChanges)
            {
                SaveAllData();
                hasUnsavedChanges = false;
            }
        }

        /// <summary>
        /// 安排延迟保存数据
        /// </summary>
        private void ScheduleDataSave()
        {
            hasUnsavedChanges = true;
            saveTimer.Stop();
            saveTimer.Start();
        }

        /// <summary>
        /// 保存所有数据
        /// </summary>
        private void SaveAllData()
        {
            try
            {
                MemoryManager.SaveMemoryItems(memoryItems);
                SettingsManager.SaveSettings(settings);
                MemoryManager.SaveStatistics(settings);
                Logger.Info("所有数据保存成功");
            }
            catch (DataAccessException ex)
            {
                ShowErrorMessage($"保存数据失败: {ex.Message}", ex);
            }
            catch (SettingsException ex)
            {
                ShowErrorMessage($"保存设置失败: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"保存数据时发生未知错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="logError">是否记录到日志</param>
        private void ShowErrorMessage(string message, bool logError = true)
        {
            if (logError)
            {
                Logger.Error(message);
            }

            MessageBox.Show(message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// 显示错误消息（包含异常信息）
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="exception">异常对象</param>
        private void ShowErrorMessage(string message, Exception exception)
        {
            Logger.Error(message, exception);
            MessageBox.Show(message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        private void InitializeTimer()
        {
            // 间隔计时器：用于两条内容之间的间隔时间
            intervalTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(settings.IntervalTimeInSeconds)
            };
            intervalTimer.Tick += IntervalTimer_Tick;

            // 根据设置初始化播放状态
            if (settings.AutoAdvance)
            {
                UpdatePlayPauseButtonState(true);
                // 如果没有开启自动朗读，立即启动计时器
                if (!settings.AutoRead)
                {
                    intervalTimer.Start();
                }
            }
            else
            {
                UpdatePlayPauseButtonState(false);
            }
        }

        private void IntervalTimer_Tick(object? sender, EventArgs e)
        {
            // 间隔时间到了，自动跳转到下一条
            intervalTimer.Stop();
            AutoAdvanceToNext();
        }

        private void AutoAdvanceToNext()
        {
            if (memoryItems.Count == 0)
                return;

            currentIndex = (currentIndex + 1) % memoryItems.Count;
            DisplayCurrentItem(); // 使用正常的显示逻辑，包括自动朗读
            SaveProgress();
        }

        private void UpdatePlayPauseButtonState(bool isPlaying)
        {
            // 更新播放/暂停按钮的状态和提示文本
            var playPauseButton = this.FindName("playPauseButton") as System.Windows.Controls.Button;
            var playPauseIcon = this.FindName("playPauseIcon") as System.Windows.Shapes.Path;

            if (playPauseButton != null && playPauseIcon != null)
            {
                if (isPlaying)
                {
                    playPauseButton.ToolTip = "暂停";
                    // 设置暂停图标
                    playPauseIcon.Data = (PathGeometry)Application.Current.Resources["PauseIcon"];
                }
                else
                {
                    playPauseButton.ToolTip = "播放";
                    // 设置播放图标
                    playPauseIcon.Data = (PathGeometry)Application.Current.Resources["PlayIcon"];
                }
            }
        }



        private void Synthesizer_SpeakCompleted(object? sender, SpeakCompletedEventArgs e)
        {
            isSpeaking = false;

            // 检查是否需要重复朗读
            currentRepeatCount++;
            if (currentRepeatCount < targetRepeatCount && !isReadingStopped)
            {
                // 需要继续重复朗读
                try
                {
                    var text = memoryItems[currentIndex].Content;

                    // 应用当前的语音设置
                    ApplySpeechSettings();

                    // 处理文本以添加智能停顿
                    var processedText = ProcessTextForSmartPause(text);

                    synthesizer?.SpeakAsync(processedText);
                    isSpeaking = true;
                    return; // 继续朗读，不进入下一条
                }
                catch (Exception ex)
                {
                    Logger.Error("重复朗读时出错", ex);
                    // 如果重复朗读出错，继续正常流程
                }
            }

            // 重复朗读完成或出错，重置计数器
            currentRepeatCount = 0;

            // 更新朗读按钮状态
            UpdateSpeakButtonState(false);

            // 朗读完成后，如果自动前进开启，启动间隔计时器
            if (settings.AutoAdvance)
            {
                // 启动间隔计时器，等待设置的间隔时间后再跳转
                intervalTimer.Start();
            }
        }

        private void LoadSettings()
        {
            // 从本地存储加载设置
            settings = SettingsManager.LoadSettings();

            // 加载统计数据
            MemoryManager.LoadStatistics(settings);

            // 应用设置到UI
            ApplySettings();
        }

        private void ApplySettings()
        {
            // 应用颜色设置
            var contentText = this.FindName("contentText") as System.Windows.Controls.TextBlock;
            var contentBorder = this.FindName("contentBorder") as System.Windows.Controls.Border;

            if (contentText != null)
            {
                contentText.Foreground = new SolidColorBrush(settings.GetTextColor());
                // 应用字体设置
                contentText.FontFamily = new FontFamily(settings.FontFamily);
                contentText.FontSize = settings.FontSize;
                contentText.FontWeight = settings.GetFontWeight();
                contentText.FontStyle = settings.GetFontStyle();
            }

            if (contentBorder != null)
            {
                contentBorder.Background = new SolidColorBrush(settings.GetBackgroundColor());
            }

            // 应用计时器设置
            if (intervalTimer != null)
            {
                // 记录当前播放状态
                bool wasPlaying = IsAutoPlaying();

                // 停止当前计时器
                intervalTimer.Stop();

                // 更新间隔时间
                intervalTimer.Interval = TimeSpan.FromSeconds(settings.IntervalTimeInSeconds);

                // 只有在之前正在播放时才恢复播放状态
                if (wasPlaying && settings.AutoAdvance)
                {
                    // 恢复播放状态，但不立即启动，让用户决定何时开始
                    UpdatePlayPauseButtonState(true);
                }
                else
                {
                    // 确保按钮状态与设置一致
                    UpdatePlayPauseButtonState(settings.AutoAdvance);
                }
            }

            // 应用语音设置
            ApplySpeechSettings();
        }

        private void LoadMemoryItems()
        {
            try
            {
                // 使用新的文件分组系统加载记忆项目
                memoryItems = MemoryManager.GetFileGroupItems(settings);

                // 如果没有项目，尝试从旧的统一列表加载
                if (memoryItems.Count == 0)
                {
                    memoryItems = MemoryManager.GetAllAvailableItems();
                }

                // 加载进度
                if (!string.IsNullOrEmpty(settings.CurrentFileGroupId))
                {
                    currentIndex = MemoryManager.LoadFileGroupProgress(settings.CurrentFileGroupId);
                }
                else
                {
                    currentIndex = MemoryManager.LoadProgress();
                }

                // 确保索引在有效范围内
                if (memoryItems.Count > 0 && (currentIndex < 0 || currentIndex >= memoryItems.Count))
                {
                    currentIndex = 0;
                }

                Logger.Info($"加载了 {memoryItems.Count} 个记忆项目，当前索引: {currentIndex}");
            }
            catch (DataAccessException ex)
            {
                ShowErrorMessage($"加载记忆内容失败: {ex.Message}", ex);
                memoryItems = new List<MemoryItem>(); // 确保有一个空列表
                currentIndex = 0;
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"加载记忆内容时发生未知错误: {ex.Message}", ex);
                memoryItems = new List<MemoryItem>(); // 确保有一个空列表
                currentIndex = 0;
            }
        }

        /// <summary>
        /// 显示当前项目的内容
        /// </summary>
        /// <param name="autoRead">是否自动朗读</param>
        /// <param name="respectUserStopChoice">是否尊重用户的停止朗读选择</param>
        private void DisplayCurrentItem(bool autoRead = true, bool respectUserStopChoice = true)
        {
            var contentText = this.FindName("contentText") as System.Windows.Controls.TextBlock;

            if (!IsValidCurrentIndex())
            {
                if (contentText != null)
                {
                    contentText.Text = "没有记忆内容。请通过设置导入内容。";
                }
                return;
            }

            try
            {
                if (contentText != null)
                {
                    contentText.Text = memoryItems[currentIndex].Content;
                }

                // 根据参数决定是否自动朗读
                bool shouldAutoRead = autoRead && settings.AutoRead && (!respectUserStopChoice || !isReadingStopped);

                if (shouldAutoRead)
                {
                    ReadCurrentItem();
                }
                else
                {
                    // 确保朗读按钮状态正确
                    UpdateSpeakButtonState(false);

                    // 如果自动播放开启但朗读被停止，启动计时器继续播放
                    if (settings.AutoAdvance && isReadingStopped)
                    {
                        intervalTimer.Start();
                    }
                }
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"显示内容时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 手动显示当前项目（不自动朗读）
        /// </summary>
        private void DisplayCurrentItemManually()
        {
            DisplayCurrentItem(autoRead: false, respectUserStopChoice: false);
        }

        /// <summary>
        /// 显示当前项目并根据用户设置决定是否自动朗读
        /// </summary>
        private void DisplayCurrentItemWithAutoRead()
        {
            DisplayCurrentItem(autoRead: true, respectUserStopChoice: true);
        }

        private void ReadCurrentItem()
        {
            if (memoryItems.Count == 0 || currentIndex < 0 || currentIndex >= memoryItems.Count)
                return;

            // 如果用户已经停止了朗读，则不执行朗读操作
            if (isReadingStopped)
                return;

            // 检查TTS是否可用
            if (!isTtsAvailable || synthesizer == null)
            {
                Logger.Warning("TTS功能不可用，跳过朗读");
                return;
            }

            try
            {
                var text = memoryItems[currentIndex].Content;

                // 停止之前的朗读
                synthesizer.SpeakAsyncCancelAll();
                isSpeaking = false;

                // 应用当前的语音设置
                ApplySpeechSettings();

                // 处理文本以添加智能停顿
                var processedText = ProcessTextForSmartPause(text);

                // 初始化重复计数器
                currentRepeatCount = 0;
                targetRepeatCount = settings.RepeatCount;

                // 开始新的朗读
                synthesizer.SpeakAsync(processedText);
                isSpeaking = true;

                // 更新朗读按钮状态
                UpdateSpeakButtonState(true);
            }
            catch (Exception ex)
            {
                Logger.Error("朗读时出错", ex);
                isSpeaking = false;
                UpdateSpeakButtonState(false);

                // 在Windows 7上可能出现的TTS错误
                if (ex.Message.Contains("SAPI") || ex.Message.Contains("Speech"))
                {
                    MessageBox.Show("语音朗读功能出现问题。\n\n建议：\n1. 检查系统音量设置\n2. 重启程序\n3. 重启计算机",
                        "朗读提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void PrevButton_Click(object sender, RoutedEventArgs e)
        {
            if (memoryItems.Count == 0)
                return;

            // 记录当前的播放状态
            bool wasAutoPlaying = IsAutoPlaying();

            // 停止间隔计时器和任何正在进行的朗读
            intervalTimer.Stop();
            synthesizer?.SpeakAsyncCancelAll();
            isSpeaking = false;
            UpdateSpeakButtonState(false);

            currentIndex = (currentIndex - 1 + memoryItems.Count) % memoryItems.Count;

            // 手动导航时显示内容，如果用户没有主动停止朗读，则自动朗读
            DisplayCurrentItemWithAutoRead();
            SaveProgress();

            // 如果之前在自动播放，恢复播放状态
            if (wasAutoPlaying && settings.AutoAdvance)
            {
                // 重新启动自动播放，但要考虑朗读状态
                if (isReadingStopped)
                {
                    // 如果朗读被停止，直接启动计时器
                    intervalTimer.Start();
                }
                // 如果朗读没有被停止，DisplayCurrentItemWithAutoRead已经处理了朗读
                // 朗读完成后会自动启动计时器
            }
        }

        private void NextButton_Click(object sender, RoutedEventArgs e)
        {
            if (memoryItems.Count == 0)
                return;

            // 记录当前的播放状态
            bool wasAutoPlaying = IsAutoPlaying();

            // 停止间隔计时器和任何正在进行的朗读
            intervalTimer.Stop();
            synthesizer?.SpeakAsyncCancelAll();
            isSpeaking = false;
            UpdateSpeakButtonState(false);

            currentIndex = (currentIndex + 1) % memoryItems.Count;

            // 手动导航时显示内容，如果用户没有主动停止朗读，则自动朗读
            DisplayCurrentItemWithAutoRead();
            SaveProgress();

            // 如果之前在自动播放，恢复播放状态
            if (wasAutoPlaying && settings.AutoAdvance)
            {
                // 重新启动自动播放，但要考虑朗读状态
                if (isReadingStopped)
                {
                    // 如果朗读被停止，直接启动计时器
                    intervalTimer.Start();
                }
                // 如果朗读没有被停止，DisplayCurrentItemWithAutoRead已经处理了朗读
                // 朗读完成后会自动启动计时器
            }
        }

        private void PlayButton_Click(object sender, RoutedEventArgs e)
        {
            // 检查TTS是否可用
            if (!isTtsAvailable || synthesizer == null)
            {
                MessageBox.Show("语音功能不可用。\n\n可能原因：\n1. 系统未安装语音包\n2. 语音服务未启动\n3. 音频设备问题",
                    "语音功能提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 切换朗读状态
            if (isSpeaking)
            {
                // 当前正在朗读，停止朗读并标记为用户主动停止
                synthesizer.SpeakAsyncCancelAll();
                isSpeaking = false;
                isReadingStopped = true; // 用户主动停止了朗读
                UpdateSpeakButtonState(false);
            }
            else
            {
                // 当前没有朗读，开始朗读并取消停止状态
                isReadingStopped = false; // 用户重新启用了朗读
                ReadCurrentItem();
                // ReadCurrentItem() 内部已经调用了 UpdateSpeakButtonState(true)，无需重复调用
            }
        }

        private void UpdateSpeakButtonState(bool isPlaying)
        {
            var playButton = this.FindName("playButton") as System.Windows.Controls.Button;
            var speakIcon = this.FindName("speakIcon") as System.Windows.Shapes.Path;

            if (playButton != null && speakIcon != null)
            {
                if (isPlaying)
                {
                    playButton.ToolTip = "停止朗读";
                    // 设置停止朗读图标
                    speakIcon.Data = (PathGeometry)Application.Current.Resources["SpeakStopIcon"];
                }
                else
                {
                    playButton.ToolTip = "朗读";
                    // 设置朗读图标
                    speakIcon.Data = (PathGeometry)Application.Current.Resources["SpeakIcon"];
                }
            }
        }

        private void PlayPauseButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换播放/暂停状态
            if (IsAutoPlaying())
            {
                // 当前正在自动播放，暂停
                StopAutoPlay();
            }
            else
            {
                // 当前已暂停，开始自动播放
                StartAutoPlay();
            }
        }

        private bool IsAutoPlaying()
        {
            // 判断是否正在自动播放：
            // 1. 计时器正在运行（等待间隔时间）
            // 2. 设置了自动前进且正在朗读（朗读完成后会启动计时器）
            // 注意：即使用户停止了朗读，只要设置了自动前进，就应该被认为是在播放状态
            return intervalTimer.IsEnabled ||
                   (settings.AutoAdvance && isSpeaking) ||
                   settings.AutoAdvance; // 简化逻辑：只要设置了自动前进就是播放状态
        }

        private void StartAutoPlay()
        {
            settings.AutoAdvance = true;
            UpdatePlayPauseButtonState(true);

            // 如果没有开启自动朗读，立即启动间隔计时器
            if (!settings.AutoRead)
            {
                intervalTimer.Start();
            }
            else
            {
                // 如果开启了自动朗读，且当前没有在朗读，且用户没有停止朗读，则开始朗读
                if (!isSpeaking && !isReadingStopped)
                {
                    ReadCurrentItem();
                }
                // 如果用户停止了朗读，直接启动计时器进行无朗读播放
                else if (isReadingStopped)
                {
                    intervalTimer.Start();
                }
                // 如果正在朗读，等朗读完成后计时器会自动启动
            }
        }

        private void StopAutoPlay()
        {
            settings.AutoAdvance = false;
            intervalTimer.Stop();
            UpdatePlayPauseButtonState(false);

            // 注意：这里不停止朗读，朗读状态由朗读/停止按钮独立控制
            // 用户可能希望停止自动播放但继续听当前内容的朗读
        }

        private void EasyButton_Click(object sender, RoutedEventArgs e)
        {
            HandleDifficultyReview(Difficulty.Easy);
        }

        private void HardButton_Click(object sender, RoutedEventArgs e)
        {
            HandleDifficultyReview(Difficulty.Hard);
        }

        /// <summary>
        /// 处理难度评价的通用方法
        /// </summary>
        /// <param name="difficulty">难度级别</param>
        private void HandleDifficultyReview(Difficulty difficulty)
        {
            if (!IsValidCurrentIndex())
                return;

            try
            {
                var currentItem = memoryItems[currentIndex];

                // 使用选定的算法标记为已复习
                currentItem.MarkAsReviewed(difficulty, settings.SelectedAlgorithm);

                // 更新统计信息
                UpdateReviewStatistics(currentItem);

                // 延迟保存数据以提高性能
                ScheduleDataSave();

                AutoAdvanceToNext();
            }
            catch (Exception ex)
            {
                ShowErrorMessage($"处理难度评价时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查当前索引是否有效
        /// </summary>
        /// <returns>索引是否有效</returns>
        private bool IsValidCurrentIndex()
        {
            return memoryItems.Count > 0 && currentIndex >= 0 && currentIndex < memoryItems.Count;
        }

        /// <summary>
        /// 更新复习统计信息
        /// </summary>
        /// <param name="currentItem">当前项目</param>
        private void UpdateReviewStatistics(MemoryItem currentItem)
        {
            settings.TodayReviewItemsStudied++;
            bool isNewItem = currentItem.ReviewCount <= 1;
            settings.UpdateStudyStats(currentItem.TotalStudyTime, isNewItem);
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            // 记录当前播放状态和进度
            bool wasAutoPlaying = IsAutoPlaying();
            int previousIndex = currentIndex; // 保存当前播放进度

            // 停止计时器和朗读
            intervalTimer.Stop();
            synthesizer?.SpeakAsyncCancelAll();
            isSpeaking = false;
            UpdateSpeakButtonState(false);

            // 打开设置对话框
            var settingsDialog = new SettingsDialog(settings)
            {
                Owner = this
            };

            if (settingsDialog.ShowDialog() == true && settingsDialog.SettingsChanged)
            {
                settings = settingsDialog.Settings;
                SettingsManager.SaveSettings(settings);
                ApplySettings();

                // 文件组列表现在在设置对话框中管理

                // 重新加载记忆项目（可能有新导入的内容）
                var previousItemsCount = memoryItems.Count;
                LoadMemoryItems();

                // 保持播放进度：只有在文件组改变或内容完全不同时才重置
                if (memoryItems.Count > 0)
                {
                    // 如果内容数量没有变化，保持之前的进度
                    if (memoryItems.Count == previousItemsCount && previousIndex < memoryItems.Count)
                    {
                        currentIndex = previousIndex; // 保持之前的播放进度
                    }
                    // 如果内容有变化，但之前的索引仍然有效，保持进度
                    else if (previousIndex < memoryItems.Count)
                    {
                        currentIndex = previousIndex; // 保持之前的播放进度
                    }
                    // 只有在之前的索引超出范围时才重置到第一项
                    else
                    {
                        currentIndex = 0;
                    }
                    SaveProgress();
                }

                // 显示当前内容，但不自动开始播放
                DisplayCurrentItemManually();

                // 如果用户之前在自动播放且设置仍然允许自动播放，则恢复播放状态
                if (wasAutoPlaying && settings.AutoAdvance)
                {
                    // 更新按钮状态为播放状态，但不立即开始播放
                    // 用户需要手动点击播放按钮来重新开始
                    UpdatePlayPauseButtonState(true);
                    settings.AutoAdvance = true; // 确保设置正确
                }
            }
            else
            {
                // 用户取消了设置，恢复之前的播放状态
                if (wasAutoPlaying && settings.AutoAdvance)
                {
                    StartAutoPlay();
                }
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void Window_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DragMove();
        }

        /// <summary>
        /// 处理键盘快捷键
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">键盘事件参数</param>
        private void MainWindow_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // 检查是否有修饰键
                bool ctrlPressed = (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control;

                switch (e.Key)
                {
                    case Key.Left:
                    case Key.A:
                        // 左箭头或A键：上一条
                        PrevButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：上一条");
                        break;

                    case Key.Right:
                    case Key.D:
                        // 右箭头或D键：下一条
                        NextButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：下一条");
                        break;

                    case Key.Space:
                        // 空格键：播放/暂停
                        PlayPauseButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：播放/暂停");
                        break;

                    case Key.R:
                        // R键：朗读/停止朗读
                        PlayButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：朗读/停止朗读");
                        break;

                    case Key.E:
                        // E键：标记为简单
                        EasyButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：标记为简单");
                        break;

                    case Key.H:
                        // H键：标记为困难
                        HardButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：标记为困难");
                        break;

                    case Key.S:
                        if (ctrlPressed)
                        {
                            // Ctrl+S：立即保存
                            SaveAllData();
                            e.Handled = true;
                            Logger.Info("键盘快捷键：立即保存");
                        }
                        else
                        {
                            // S键：打开设置
                            SettingsButton_Click(this, new RoutedEventArgs());
                            e.Handled = true;
                            Logger.Info("键盘快捷键：打开设置");
                        }
                        break;

                    case Key.Escape:
                        // ESC键：关闭程序
                        CloseButton_Click(this, new RoutedEventArgs());
                        e.Handled = true;
                        Logger.Info("键盘快捷键：关闭程序");
                        break;

                    case Key.F1:
                        // F1键：显示帮助
                        ShowKeyboardShortcutsHelp();
                        e.Handled = true;
                        break;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("处理键盘快捷键时出错", ex);
            }
        }

        /// <summary>
        /// 显示键盘快捷键帮助
        /// </summary>
        private void ShowKeyboardShortcutsHelp()
        {
            var helpMessage = @"键盘快捷键：

导航：
← / A    上一条内容
→ / D    下一条内容

控制：
空格键    播放/暂停自动播放
R        朗读/停止朗读当前内容

评价：
E        标记为简单
H        标记为困难

其他：
S        打开设置
Ctrl+S   立即保存数据
ESC      关闭程序
F1       显示此帮助";

            MessageBox.Show(helpMessage, "键盘快捷键帮助", MessageBoxButton.OK, MessageBoxImage.Information);
            Logger.Info("显示键盘快捷键帮助");
        }



        private void SaveProgress()
        {
            // 如果选择了文件组，保存文件组进度；否则保存旧的统一进度
            if (!string.IsNullOrEmpty(settings.CurrentFileGroupId))
            {
                MemoryManager.SaveFileGroupProgress(settings.CurrentFileGroupId, currentIndex);
            }
            else
            {
                MemoryManager.SaveProgress(currentIndex);
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // 停止所有计时器
                intervalTimer?.Stop();
                saveTimer?.Stop();

                // 结束所有项目的学习会话
                foreach (var item in memoryItems)
                {
                    item.EndStudySession();
                }

                // 立即保存所有数据
                SaveProgress();
                SaveAllData();

                // 释放语音合成器资源
                synthesizer?.Dispose();
            }
            catch (Exception ex)
            {
                // 记录错误但不阻止程序关闭
                System.Diagnostics.Debug.WriteLine($"关闭程序时出错: {ex.Message}");
            }
            finally
            {
                base.OnClosed(e);
            }
        }
    }
}