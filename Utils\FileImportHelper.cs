using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using OfficeOpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using MemoryEnhancer.Exceptions;
using Markdig;

namespace MemoryEnhancer.Utils
{
    /// <summary>
    /// 文件导入辅助类
    /// </summary>
    public static class FileImportHelper
    {
        /// <summary>
        /// 根据文件扩展名确定导入方法并执行导入
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="items">要添加项目的列表</param>
        /// <returns>导入是否成功</returns>
        public static bool ImportFile(string filePath, List<MemoryItem> items)
        {
            if (!File.Exists(filePath))
            {
                throw new ImportException(Constants.FileNotFoundMessage, filePath);
            }

            var extension = Path.GetExtension(filePath).ToLower();

            switch (extension)
            {
                case ".txt":
                    return ImportFromTxt(filePath, items);
                case ".csv":
                    return ImportFromCsv(filePath, items);
                case ".json":
                    return ImportFromJson(filePath, items);
                case ".xlsx":
                case ".xls":
                    return ImportFromExcel(filePath, items);
                case ".docx":
                    return ImportFromWordDocx(filePath, items);
                case ".md":
                case ".markdown":
                    return ImportFromMarkdown(filePath, items);
                default:
                    return ImportFromTxt(filePath, items); // 默认按文本文件处理
            }
        }

        /// <summary>
        /// 检查文件扩展名是否受支持
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否受支持</returns>
        public static bool IsSupportedFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return Constants.AllSupportedExtensions.Contains(extension);
        }

        /// <summary>
        /// 从文本文件导入
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="items">要添加项目的列表</param>
        /// <returns>导入是否成功</returns>
        private static bool ImportFromTxt(string filePath, List<MemoryItem> items)
        {
            try
            {
                var content = File.ReadAllText(filePath);
                var lines = content.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.RemoveEmptyEntries);

                int count = 0;
                foreach (var line in lines)
                {
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        items.Add(new MemoryItem(line.Trim()));
                        count++;
                    }
                }

                Logger.Info($"从文本文件导入了 {count} 个项目");
                return count > 0;
            }
            catch (Exception ex)
            {
                Logger.Error($"从文本文件导入时出错: {ex.Message}", ex);
                throw new ImportException($"从文本文件导入时出错: {ex.Message}", filePath, ex);
            }
        }

        /// <summary>
        /// 从CSV文件导入
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="items">要添加项目的列表</param>
        /// <returns>导入是否成功</returns>
        private static bool ImportFromCsv(string filePath, List<MemoryItem> items)
        {
            try
            {
                var content = File.ReadAllText(filePath);
                var lines = content.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.RemoveEmptyEntries);

                int count = 0;
                foreach (var line in lines)
                {
                    if (!string.IsNullOrWhiteSpace(line))
                    {
                        // 分割CSV行，取第一列作为内容
                        var columns = line.Split(',');
                        if (columns.Length > 0 && !string.IsNullOrWhiteSpace(columns[0]))
                        {
                            // 移除可能的引号
                            var cellContent = columns[0].Trim().Trim('"');
                            items.Add(new MemoryItem(cellContent));
                            count++;
                        }
                    }
                }

                Logger.Info($"从CSV文件导入了 {count} 个项目");
                return count > 0;
            }
            catch (Exception ex)
            {
                Logger.Error($"从CSV文件导入时出错: {ex.Message}", ex);
                throw new ImportException($"从CSV文件导入时出错: {ex.Message}", filePath, ex);
            }
        }

        /// <summary>
        /// 从JSON文件导入
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="items">要添加项目的列表</param>
        /// <returns>导入是否成功</returns>
        private static bool ImportFromJson(string filePath, List<MemoryItem> items)
        {
            try
            {
                var json = File.ReadAllText(filePath);

                // 尝试解析为字符串数组
                try
                {
                    var contentArray = JsonSerializer.Deserialize<string[]>(json, Constants.GetJsonSerializerOptions());
                    if (contentArray != null && contentArray.Length > 0)
                    {
                        int count = 0;
                        foreach (var content in contentArray)
                        {
                            if (!string.IsNullOrWhiteSpace(content))
                            {
                                items.Add(new MemoryItem(content.Trim()));
                                count++;
                            }
                        }
                        Logger.Info($"从JSON文件导入了 {count} 个项目（字符串数组格式）");
                        return count > 0;
                    }
                }
                catch
                {
                    // 如果字符串数组解析失败，尝试对象数组格式
                }

                // 尝试解析为对象数组，每个对象有content属性
                var objectArray = JsonSerializer.Deserialize<JsonElement>(json, Constants.GetJsonSerializerOptions());

                if (objectArray.ValueKind == JsonValueKind.Array)
                {
                    int count = 0;
                    foreach (var item in objectArray.EnumerateArray())
                    {
                        if (item.TryGetProperty("content", out JsonElement contentElement) ||
                            item.TryGetProperty("Content", out contentElement))
                        {
                            string? content = contentElement.GetString();
                            if (!string.IsNullOrWhiteSpace(content))
                            {
                                items.Add(new MemoryItem(content.Trim()));
                                count++;
                            }
                        }
                    }
                    Logger.Info($"从JSON文件导入了 {count} 个项目（对象数组格式）");
                    return count > 0;
                }

                throw new ImportException("JSON格式不正确，请确保是字符串数组或包含content字段的对象数组", filePath);
            }
            catch (ImportException)
            {
                throw; // 重新抛出ImportException
            }
            catch (Exception ex)
            {
                Logger.Error($"从JSON文件导入时出错: {ex.Message}", ex);
                throw new ImportException($"从JSON文件导入时出错: {ex.Message}", filePath, ex);
            }
        }

        /// <summary>
        /// 从Excel文件导入
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="items">要添加项目的列表</param>
        /// <returns>导入是否成功</returns>
        private static bool ImportFromExcel(string filePath, List<MemoryItem> items)
        {
            try
            {
                // EPPlus 4.5.3.3 不需要设置许可证上下文

                using (var package = new ExcelPackage(new FileInfo(filePath)))
                {
                    var worksheet = package.Workbook.Worksheets[0]; // 使用第一个工作表
                    int rowCount = worksheet.Dimension?.Rows ?? 0;
                    int count = 0;

                    for (int row = 1; row <= rowCount; row++)
                    {
                        var cellValue = worksheet.Cells[row, 1].Value?.ToString(); // 读取第一列
                        if (!string.IsNullOrWhiteSpace(cellValue))
                        {
                            items.Add(new MemoryItem(cellValue.Trim()));
                            count++;
                        }
                    }

                    Logger.Info($"从Excel文件导入了 {count} 个项目");
                    return count > 0;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"从Excel文件导入时出错: {ex.Message}", ex);
                throw new ImportException($"从Excel文件导入时出错: {ex.Message}", filePath, ex);
            }
        }

        /// <summary>
        /// 从Word .docx文件导入
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="items">要添加项目的列表</param>
        /// <returns>导入是否成功</returns>
        private static bool ImportFromWordDocx(string filePath, List<MemoryItem> items)
        {
            try
            {
                // 检查文件是否存在且可读
                if (!File.Exists(filePath))
                {
                    throw new ImportException("Word文件不存在", filePath);
                }

                // 检查文件大小（避免处理过大的文件）
                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length == 0)
                {
                    throw new ImportException("Word文件为空", filePath);
                }
                if (fileInfo.Length > 50 * 1024 * 1024) // 50MB限制
                {
                    throw new ImportException("Word文件过大（超过50MB），请使用较小的文件", filePath);
                }

                // 检查文件是否被占用
                try
                {
                    using (var fileStream = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
                    {
                        // 检查文件头，确保是有效的.docx文档
                        var buffer = new byte[4];
                        fileStream.Read(buffer, 0, 4);

                        // .docx文件应该以ZIP文件头开始 (PK)
                        if (buffer[0] != 0x50 || buffer[1] != 0x4B)
                        {
                            throw new ImportException("Word文件格式损坏或不是有效的.docx文档", filePath);
                        }
                    }
                }
                catch (IOException ex)
                {
                    throw new ImportException($"Word文件被占用或无法访问: {ex.Message}", filePath, ex);
                }

                using (var document = WordprocessingDocument.Open(filePath, false))
                {
                    if (document.MainDocumentPart == null)
                    {
                        throw new ImportException("Word文档格式无效：缺少主文档部分", filePath);
                    }

                    var body = document.MainDocumentPart.Document?.Body;
                    if (body == null)
                    {
                        throw new ImportException("Word文档结构无效：缺少文档主体", filePath);
                    }

                    int count = 0;
                    var paragraphs = body.Elements<DocumentFormat.OpenXml.Wordprocessing.Paragraph>();

                    foreach (var paragraph in paragraphs)
                    {
                        try
                        {
                            var text = paragraph.InnerText?.Trim();
                            if (!string.IsNullOrWhiteSpace(text))
                            {
                                items.Add(new MemoryItem(text));
                                count++;
                            }
                        }
                        catch (Exception ex)
                        {
                            Logger.Warning($"处理Word段落时出错，跳过该段落: {ex.Message}");
                            // 继续处理其他段落
                        }
                    }

                    Logger.Info($"从Word文件导入了 {count} 个项目");

                    if (count == 0)
                    {
                        throw new ImportException("Word文档中没有找到有效的文本内容", filePath);
                    }

                    return count > 0;
                }
            }
            catch (ImportException)
            {
                throw; // 重新抛出ImportException
            }
            catch (System.IO.InvalidDataException ex) when (ex.Message.Contains("Central Directory"))
            {
                Logger.Error($"Word文件格式错误: {ex.Message}", ex);
                throw new ImportException("Word文件格式损坏，请检查文件是否完整或尝试重新保存文件", filePath, ex);
            }
            catch (Exception ex)
            {
                Logger.Error($"从Word文件导入时出错: {ex.Message}", ex);

                // 提供更友好的错误信息
                string friendlyMessage = ex.Message;
                if (ex.Message.Contains("Central Directory"))
                {
                    friendlyMessage = "Word文件格式损坏，请检查文件是否完整";
                }
                else if (ex.Message.Contains("not a valid"))
                {
                    friendlyMessage = "不是有效的Word文档格式";
                }
                else if (ex.Message.Contains("Access"))
                {
                    friendlyMessage = "无法访问Word文件，请检查文件权限";
                }

                throw new ImportException($"从Word文件导入时出错: {friendlyMessage}", filePath, ex);
            }
        }



        /// <summary>
        /// 从Markdown文件导入
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="items">要添加项目的列表</param>
        /// <returns>导入是否成功</returns>
        private static bool ImportFromMarkdown(string filePath, List<MemoryItem> items)
        {
            try
            {
                // 检查文件是否存在且可读
                if (!File.Exists(filePath))
                {
                    throw new ImportException("Markdown文件不存在", filePath);
                }

                // 检查文件大小（避免处理过大的文件）
                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length == 0)
                {
                    throw new ImportException("Markdown文件为空", filePath);
                }
                if (fileInfo.Length > 10 * 1024 * 1024) // 10MB限制
                {
                    throw new ImportException("Markdown文件过大（超过10MB），请使用较小的文件", filePath);
                }

                var content = File.ReadAllText(filePath);

                // 使用Markdig解析Markdown
                var pipeline = new MarkdownPipelineBuilder().UseAdvancedExtensions().Build();
                var document = Markdig.Parsers.MarkdownParser.Parse(content, pipeline);

                int count = 0;

                // 提取文本内容
                foreach (var block in document)
                {
                    try
                    {
                        string text = "";

                        // 处理不同类型的块
                        if (block is Markdig.Syntax.ParagraphBlock paragraph)
                        {
                            text = ExtractTextFromInlines(paragraph.Inline);
                        }
                        else if (block is Markdig.Syntax.HeadingBlock heading)
                        {
                            text = ExtractTextFromInlines(heading.Inline);
                        }
                        else if (block is Markdig.Syntax.ListBlock list)
                        {
                            foreach (var listItem in list)
                            {
                                if (listItem is Markdig.Syntax.ListItemBlock item)
                                {
                                    foreach (var itemBlock in item)
                                    {
                                        if (itemBlock is Markdig.Syntax.ParagraphBlock itemParagraph)
                                        {
                                            var itemText = ExtractTextFromInlines(itemParagraph.Inline);
                                            if (!string.IsNullOrWhiteSpace(itemText))
                                            {
                                                items.Add(new MemoryItem(itemText.Trim()));
                                                count++;
                                            }
                                        }
                                    }
                                }
                            }
                            continue; // 列表项已处理，跳过后续处理
                        }
                        else if (block is Markdig.Syntax.QuoteBlock quote)
                        {
                            foreach (var quoteBlock in quote)
                            {
                                if (quoteBlock is Markdig.Syntax.ParagraphBlock quoteParagraph)
                                {
                                    var quoteText = ExtractTextFromInlines(quoteParagraph.Inline);
                                    if (!string.IsNullOrWhiteSpace(quoteText))
                                    {
                                        items.Add(new MemoryItem($"引用: {quoteText.Trim()}"));
                                        count++;
                                    }
                                }
                            }
                            continue; // 引用块已处理，跳过后续处理
                        }

                        // 处理普通文本块
                        if (!string.IsNullOrWhiteSpace(text))
                        {
                            items.Add(new MemoryItem(text.Trim()));
                            count++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Warning($"处理Markdown块时出错，跳过该块: {ex.Message}");
                        // 继续处理其他块
                    }
                }

                Logger.Info($"从Markdown文件导入了 {count} 个项目");

                if (count == 0)
                {
                    throw new ImportException("Markdown文档中没有找到有效的文本内容", filePath);
                }

                return count > 0;
            }
            catch (ImportException)
            {
                throw; // 重新抛出ImportException
            }
            catch (Exception ex)
            {
                Logger.Error($"从Markdown文件导入时出错: {ex.Message}", ex);
                throw new ImportException($"从Markdown文件导入时出错: {ex.Message}", filePath, ex);
            }
        }

        /// <summary>
        /// 从Markdown内联元素中提取文本
        /// </summary>
        /// <param name="inline">内联元素</param>
        /// <returns>提取的文本</returns>
        private static string ExtractTextFromInlines(Markdig.Syntax.Inlines.ContainerInline? inline)
        {
            if (inline == null) return "";

            var text = "";
            foreach (var inlineElement in inline)
            {
                if (inlineElement is Markdig.Syntax.Inlines.LiteralInline literal)
                {
                    text += literal.Content.ToString();
                }
                else if (inlineElement is Markdig.Syntax.Inlines.EmphasisInline emphasis)
                {
                    text += ExtractTextFromInlines(emphasis);
                }
                else if (inlineElement is Markdig.Syntax.Inlines.LinkInline link)
                {
                    text += ExtractTextFromInlines(link);
                }
                else if (inlineElement is Markdig.Syntax.Inlines.CodeInline code)
                {
                    text += code.Content;
                }
            }
            return text;
        }
    }
}
