# 如何创建"我要记忆"安装程序

## 📋 **概述**

现在您有了三种方式来创建需要安装才能使用的Windows安装程序：

1. **NSIS安装程序** (推荐) - 生成 `.exe` 安装文件
2. **Inno Setup安装程序** - 生成 `.exe` 安装文件  
3. **WiX MSI安装包** - 生成 `.msi` 安装文件

## 🛠️ **准备工作**

### **1. 确保有自包含发布版本**
```bash
dotnet publish MemoryEnhancer.csproj --configuration Release --output publish-standalone --runtime win-x64 --self-contained true
```

### **2. 安装安装程序构建工具**

#### **选项1: NSIS (推荐)**
- 下载地址: https://nsis.sourceforge.io/Download
- 安装后将安装目录添加到 PATH 环境变量
- 优点: 小巧、快速、功能强大

#### **选项2: Inno Setup**
- 下载地址: https://jrsoftware.org/isdl.php
- 安装后将安装目录添加到 PATH 环境变量
- 优点: 界面友好、功能丰富

#### **选项3: WiX Toolset**
- 下载地址: https://wixtoolset.org/releases/
- 或使用命令: `dotnet tool install --global wix`
- 优点: 微软官方工具、MSI格式

## 🚀 **构建安装程序**

### **方法1: 使用批处理脚本 (Windows)**
```cmd
build-installer.bat
```

### **方法2: 使用PowerShell脚本**
```powershell
.\Build-Installer.ps1
```

### **方法3: 手动构建**

#### **使用NSIS:**
```cmd
makensis installer.nsi
```

#### **使用Inno Setup:**
```cmd
iscc installer-setup.iss
```

#### **使用WiX:**
```cmd
candle installer.wxs -out installer.wixobj
light installer.wixobj -out 我要记忆_v1.0_Windows10+.msi -ext WixUIExtension
```

## 📦 **生成的安装程序特性**

### **✅ 安装功能**
- **系统要求检查**: 自动检查Windows 10+ x64系统
- **管理员权限**: 需要管理员权限安装到Program Files
- **自包含运行时**: 包含完整的.NET 6.0运行时，无需用户安装
- **文件关联**: 自动关联.memory文件格式
- **开始菜单**: 创建开始菜单程序组和快捷方式
- **桌面快捷方式**: 可选创建桌面快捷方式
- **注册表集成**: 正确注册程序信息

### **✅ 卸载功能**
- **完整卸载**: 通过控制面板或开始菜单卸载
- **清理注册表**: 自动清理所有注册表项
- **保留用户数据**: 卸载时保留用户学习数据
- **快捷方式清理**: 自动删除所有快捷方式

### **✅ 用户体验**
- **现代化界面**: 使用现代UI风格
- **中文界面**: 完整的中文安装界面
- **进度显示**: 显示安装进度
- **错误处理**: 友好的错误提示
- **安装后启动**: 可选择安装完成后立即运行程序

## 📁 **文件结构**

安装后的程序文件结构：
```
C:\Program Files\我要记忆\
├── MemoryEnhancer.exe          # 主程序
├── MemoryEnhancer.dll          # 程序库
├── *.dll                       # 运行时库和依赖库
├── Resources\                  # 资源文件
│   ├── icon.ico               # 程序图标
│   └── *.png                  # 各尺寸图标
├── zh-Hans\                   # 中文语言包
├── README.md                  # 说明文档
├── LICENSE.txt                # 许可协议
├── 安装和使用说明.md           # 使用说明
└── uninst.exe                 # 卸载程序
```

## 🎯 **安装程序对比**

| 特性 | NSIS | Inno Setup | WiX MSI |
|------|------|------------|---------|
| 文件大小 | 小 | 中等 | 大 |
| 安装速度 | 快 | 中等 | 慢 |
| 自定义程度 | 高 | 高 | 中等 |
| 企业部署 | 支持 | 支持 | 最佳 |
| 学习难度 | 中等 | 简单 | 复杂 |
| 推荐程度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |

## 🔧 **故障排除**

### **构建失败**
1. 确保安装了构建工具并添加到PATH
2. 检查publish-standalone文件夹是否存在
3. 确保所有必要文件都在正确位置

### **安装失败**
1. 确保以管理员权限运行安装程序
2. 检查系统是否为Windows 10+ x64
3. 确保有足够的磁盘空间

### **程序无法启动**
1. 检查安装是否完整
2. 查看Windows事件日志
3. 尝试重新安装

## 📞 **技术支持**

如果遇到问题，请检查：
1. 系统要求是否满足
2. 安装日志文件
3. Windows事件查看器
4. 程序安装目录下的日志文件

## 🎉 **完成**

现在您有了一个完整的Windows安装程序，用户可以：
1. 下载安装程序
2. 以管理员权限运行
3. 按照向导完成安装
4. 从开始菜单或桌面启动程序
5. 通过控制面板卸载程序

**这是一个真正需要安装才能使用的Windows程序！** 🚀
