<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="brainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#357ABD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E5F8A;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="lightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- 背景圆形 -->
  <circle cx="128" cy="128" r="120" fill="url(#brainGradient)" filter="url(#shadow)"/>

  <!-- 大脑主体 -->
  <g transform="translate(128, 128)">
    <!-- 左脑半球 -->
    <path d="M -45 -35
             C -65 -35, -75 -20, -75 0
             C -75 20, -65 35, -45 35
             C -25 35, -15 20, -15 0
             C -15 -20, -25 -35, -45 -35 Z"
          fill="#FFFFFF" opacity="0.9"/>

    <!-- 右脑半球 -->
    <path d="M 15 -35
             C 25 -35, 35 -20, 35 0
             C 35 20, 25 35, 15 35
             C 45 35, 75 20, 75 0
             C 75 -20, 45 -35, 15 -35 Z"
          fill="#FFFFFF" opacity="0.9"/>

    <!-- 大脑沟回纹理 -->
    <g stroke="#4A90E2" stroke-width="2" fill="none" opacity="0.6">
      <!-- 左脑纹理 -->
      <path d="M -60 -20 Q -45 -15, -30 -20"/>
      <path d="M -65 -5 Q -50 0, -35 -5"/>
      <path d="M -60 10 Q -45 15, -30 10"/>
      <path d="M -55 25 Q -40 20, -25 25"/>

      <!-- 右脑纹理 -->
      <path d="M 25 -20 Q 40 -15, 55 -20"/>
      <path d="M 30 -5 Q 45 0, 60 -5"/>
      <path d="M 25 10 Q 40 15, 55 10"/>
      <path d="M 30 25 Q 45 20, 60 25"/>
    </g>

    <!-- 中央分割线 -->
    <line x1="0" y1="-35" x2="0" y2="35" stroke="#4A90E2" stroke-width="3" opacity="0.7"/>

    <!-- 记忆符号 - 灯泡 -->
    <g transform="translate(0, -60)">
      <!-- 灯泡主体 -->
      <ellipse cx="0" cy="0" rx="12" ry="15" fill="url(#lightGradient)" opacity="0.9"/>

      <!-- 灯泡底座 -->
      <rect x="-8" y="12" width="16" height="6" rx="2" fill="#E0E0E0"/>

      <!-- 光芒 -->
      <g stroke="url(#lightGradient)" stroke-width="2" opacity="0.8">
        <line x1="-20" y1="-8" x2="-16" y2="-4"/>
        <line x1="16" y1="-4" x2="20" y2="-8"/>
        <line x1="-18" y1="8" x2="-14" y2="4"/>
        <line x1="14" y1="4" x2="18" y2="8"/>
        <line x1="0" y1="-25" x2="0" y2="-21"/>
      </g>
    </g>

    <!-- 记忆点 -->
    <g fill="url(#lightGradient)" opacity="0.7">
      <circle cx="-40" cy="-10" r="3"/>
      <circle cx="40" cy="-10" r="3"/>
      <circle cx="-30" cy="15" r="2"/>
      <circle cx="35" cy="15" r="2"/>
    </g>
  </g>

  <!-- 底部文字 -->
  <text x="128" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#FFFFFF" opacity="0.8">
    我要记忆
  </text>
</svg>
